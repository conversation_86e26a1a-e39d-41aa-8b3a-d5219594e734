/* Estilos para el seguimiento de pedidos */

.order-tracking {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.tracking-header {
  text-align: center;
  margin-bottom: 3rem;
}

.tracking-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tracking-header p {
  font-size: 1.2rem;
  color: #7f8c8d;
}

/* Búsqueda */
.tracking-search {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  margin-bottom: 2rem;
}

.search-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.search-type-selector {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.type-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e1e8ed;
  border-radius: 25px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.type-btn:hover {
  border-color: #3498db;
  transform: translateY(-1px);
}

.type-btn.active {
  border-color: #3498db;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.search-input-container {
  display: flex;
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
}

.search-input {
  flex: 1;
  padding: 1rem 1.5rem;
  border: 2px solid #e1e8ed;
  border-radius: 25px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-btn {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 140px;
  justify-content: center;
}

.search-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.search-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  padding: 1rem;
  border-radius: 10px;
  margin-top: 1rem;
  font-weight: 500;
}

/* Resultados */
.tracking-results {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.order-summary {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.summary-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.summary-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-weight: 500;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.detail-value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

/* Timeline */
.tracking-timeline {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.tracking-timeline h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2rem;
}

.timeline {
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e1e8ed;
}

.timeline-item {
  position: relative;
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: relative;
  z-index: 2;
  width: 40px;
  height: 40px;
  background: white;
  border: 3px solid #3498db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.timeline-icon {
  font-size: 1.2rem;
}

.timeline-content {
  flex: 1;
  background: #f8f9fa;
  border-radius: 10px;
  padding: 1.5rem;
  border: 1px solid #e1e8ed;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.timeline-header h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  line-height: 1.3;
}

.timeline-date {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-weight: 500;
  white-space: nowrap;
}

.timeline-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.timeline-location,
.timeline-carrier {
  margin: 0;
  font-size: 0.9rem;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.no-events {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
}

/* Acciones */
.tracking-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.new-search-btn,
.print-btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.new-search-btn {
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

.print-btn {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: white;
  box-shadow: 0 4px 15px rgba(149, 165, 166, 0.4);
}

.new-search-btn:hover,
.print-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.6);
}

.print-btn:hover {
  box-shadow: 0 6px 20px rgba(149, 165, 166, 0.6);
}

/* Responsive design */
@media (max-width: 768px) {
  .order-tracking {
    padding: 1rem;
  }
  
  .tracking-header h2 {
    font-size: 2rem;
  }
  
  .search-type-selector {
    flex-direction: column;
  }
  
  .search-input-container {
    flex-direction: column;
  }
  
  .summary-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .summary-details {
    grid-template-columns: 1fr;
  }
  
  .timeline-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
  
  .timeline-date {
    white-space: normal;
  }
  
  .tracking-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .timeline::before {
    left: 15px;
  }
  
  .timeline-marker {
    width: 30px;
    height: 30px;
  }
  
  .timeline-icon {
    font-size: 1rem;
  }
  
  .timeline-content {
    padding: 1rem;
  }
}
