"""
Servicio de detección de rostros usando MediaPipe.

Ruta: app/services/face_detection.py
Responsabilidad: Detectar rostros y puntos clave faciales
"""

import cv2
import numpy as np
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    mp = None

from typing import List, Dict, Tuple, Optional
import structlog

from app.core.config import settings

logger = structlog.get_logger()


class FaceDetectionService:
    """Servicio para detectar rostros y puntos clave faciales."""
    
    def __init__(self):
        """Inicializa el servicio de detección facial."""
        if not MEDIAPIPE_AVAILABLE:
            logger.warning("MediaPipe no está disponible. Usando detección básica con OpenCV.")
            self.use_mediapipe = False
            # Cargar clasificador de rostros de OpenCV
            try:
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            except Exception as e:
                logger.error(f"Error cargando clasificador de rostros: {e}")
                self.face_cascade = None
        else:
            self.use_mediapipe = True
            self.mp_face_detection = mp.solutions.face_detection
            self.mp_face_mesh = mp.solutions.face_mesh
            self.mp_drawing = mp.solutions.drawing_utils

            # Configurar detectores
            try:
                self.face_detection = self.mp_face_detection.FaceDetection(
                    model_selection=0,
                    min_detection_confidence=settings.DETECTION_CONFIDENCE
                )

                self.face_mesh = self.mp_face_mesh.FaceMesh(
                    static_image_mode=True,
                    max_num_faces=5,
                    refine_landmarks=True,
                    min_detection_confidence=settings.DETECTION_CONFIDENCE,
                    min_tracking_confidence=settings.TRACKING_CONFIDENCE
                )
            except Exception as e:
                logger.error(f"Error inicializando MediaPipe: {e}")
                self.use_mediapipe = False
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

        logger.info(f"FaceDetectionService inicializado (MediaPipe: {self.use_mediapipe})")
    
    def detect_faces(self, image: np.ndarray) -> List[Dict]:
        """
        Detecta rostros en una imagen.

        Args:
            image: Imagen en formato OpenCV (BGR)

        Returns:
            Lista de diccionarios con información de rostros detectados
        """
        try:
            if self.use_mediapipe:
                return self._detect_faces_mediapipe(image)
            else:
                return self._detect_faces_opencv(image)

        except Exception as e:
            logger.error(f"Error en detección de rostros: {str(e)}")
            return []

    def _detect_faces_mediapipe(self, image: np.ndarray) -> List[Dict]:
        """Detecta rostros usando MediaPipe."""
        # Convertir BGR a RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        height, width = image.shape[:2]

        # Detectar rostros
        results = self.face_detection.process(rgb_image)
        faces = []

        if results.detections:
            for detection in results.detections:
                # Obtener bounding box
                bbox = detection.location_data.relative_bounding_box

                # Convertir coordenadas relativas a absolutas
                x = int(bbox.xmin * width)
                y = int(bbox.ymin * height)
                w = int(bbox.width * width)
                h = int(bbox.height * height)

                # Asegurar que las coordenadas estén dentro de la imagen
                x = max(0, x)
                y = max(0, y)
                w = min(w, width - x)
                h = min(h, height - y)

                face_info = {
                    'x': x,
                    'y': y,
                    'width': w,
                    'height': h,
                    'confidence': detection.score[0],
                    'landmarks': self._get_face_landmarks(rgb_image, x, y, w, h)
                }

                faces.append(face_info)

            logger.info(f"Detectados {len(faces)} rostros con MediaPipe")
        else:
            logger.warning("No se detectaron rostros en la imagen")

        return faces

    def _detect_faces_opencv(self, image: np.ndarray) -> List[Dict]:
        """Detecta rostros usando OpenCV como fallback."""
        if self.face_cascade is None:
            logger.error("Clasificador de rostros no disponible")
            return []

        # Convertir a escala de grises
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Detectar rostros
        faces_rects = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=5,
            minSize=(30, 30)
        )

        faces = []
        for (x, y, w, h) in faces_rects:
            face_info = {
                'x': int(x),
                'y': int(y),
                'width': int(w),
                'height': int(h),
                'confidence': 0.8,  # Confianza estimada para OpenCV
                'landmarks': None  # OpenCV básico no proporciona landmarks
            }
            faces.append(face_info)

        logger.info(f"Detectados {len(faces)} rostros con OpenCV")
        return faces
    
    def _get_face_landmarks(self, rgb_image: np.ndarray, x: int, y: int, w: int, h: int) -> Optional[Dict]:
        """
        Obtiene puntos clave faciales para una región de rostro.
        
        Args:
            rgb_image: Imagen en RGB
            x, y, w, h: Coordenadas del rostro
            
        Returns:
            Diccionario con puntos clave faciales
        """
        try:
            # Extraer región del rostro
            face_region = rgb_image[y:y+h, x:x+w]
            
            if face_region.size == 0:
                return None
            
            # Obtener landmarks
            results = self.face_mesh.process(face_region)
            
            if results.multi_face_landmarks:
                landmarks = results.multi_face_landmarks[0]
                
                # Puntos clave importantes para lentes
                key_points = {
                    'left_eye_center': self._get_eye_center(landmarks, 'left', w, h),
                    'right_eye_center': self._get_eye_center(landmarks, 'right', w, h),
                    'nose_tip': self._get_nose_tip(landmarks, w, h),
                    'left_eye_corner': self._get_eye_corner(landmarks, 'left', w, h),
                    'right_eye_corner': self._get_eye_corner(landmarks, 'right', w, h)
                }
                
                # Ajustar coordenadas al sistema global
                for point_name, point in key_points.items():
                    if point:
                        key_points[point_name] = (point[0] + x, point[1] + y)
                
                return key_points
                
        except Exception as e:
            logger.error(f"Error obteniendo landmarks: {str(e)}")
            
        return None
    
    def _get_eye_center(self, landmarks, eye: str, width: int, height: int) -> Optional[Tuple[int, int]]:
        """Calcula el centro del ojo."""
        try:
            if eye == 'left':
                # Puntos del ojo izquierdo (desde la perspectiva de la persona)
                eye_points = [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246]
            else:
                # Puntos del ojo derecho
                eye_points = [362, 382, 381, 380, 374, 373, 390, 249, 263, 466, 388, 387, 386, 385, 384, 398]
            
            x_coords = []
            y_coords = []
            
            for point_idx in eye_points:
                if point_idx < len(landmarks.landmark):
                    landmark = landmarks.landmark[point_idx]
                    x_coords.append(landmark.x * width)
                    y_coords.append(landmark.y * height)
            
            if x_coords and y_coords:
                center_x = int(sum(x_coords) / len(x_coords))
                center_y = int(sum(y_coords) / len(y_coords))
                return (center_x, center_y)
                
        except Exception as e:
            logger.error(f"Error calculando centro del ojo {eye}: {str(e)}")
            
        return None
    
    def _get_eye_corner(self, landmarks, eye: str, width: int, height: int) -> Optional[Tuple[int, int]]:
        """Obtiene la esquina externa del ojo."""
        try:
            if eye == 'left':
                corner_idx = 33  # Esquina externa del ojo izquierdo
            else:
                corner_idx = 362  # Esquina externa del ojo derecho
            
            if corner_idx < len(landmarks.landmark):
                landmark = landmarks.landmark[corner_idx]
                x = int(landmark.x * width)
                y = int(landmark.y * height)
                return (x, y)
                
        except Exception as e:
            logger.error(f"Error obteniendo esquina del ojo {eye}: {str(e)}")
            
        return None
    
    def _get_nose_tip(self, landmarks, width: int, height: int) -> Optional[Tuple[int, int]]:
        """Obtiene la punta de la nariz."""
        try:
            nose_tip_idx = 1  # Índice de la punta de la nariz
            
            if nose_tip_idx < len(landmarks.landmark):
                landmark = landmarks.landmark[nose_tip_idx]
                x = int(landmark.x * width)
                y = int(landmark.y * height)
                return (x, y)
                
        except Exception as e:
            logger.error(f"Error obteniendo punta de la nariz: {str(e)}")
            
        return None
    
    def __del__(self):
        """Limpia recursos al destruir el objeto."""
        if hasattr(self, 'face_detection'):
            self.face_detection.close()
        if hasattr(self, 'face_mesh'):
            self.face_mesh.close()
