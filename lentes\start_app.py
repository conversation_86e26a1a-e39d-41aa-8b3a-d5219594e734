#!/usr/bin/env python3
"""
Script para iniciar la aplicación de simulación de lentes.

Este script inicia tanto el backend como el frontend de la aplicación.
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path
import signal
import os

def print_banner():
    """Imprime el banner de la aplicación."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║               🕶️  SIMULADOR DE LENTES VIRTUAL  🕶️              ║
    ║                                                              ║
    ║                   Iniciando aplicación...                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """Verifica que las dependencias estén instaladas."""
    print("🔍 Verificando dependencias...")
    
    # Verificar Python
    try:
        import fastapi
        import uvicorn
        import cv2
        import mediapipe
        print("✅ Dependencias de Python instaladas")
    except ImportError as e:
        print(f"❌ Error: Falta dependencia de Python: {e}")
        print("💡 Ejecuta: pip install -r requirements.txt")
        return False
    
    # Verificar Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js instalado: {result.stdout.strip()}")
        else:
            print("❌ Node.js no encontrado")
            return False
    except FileNotFoundError:
        print("❌ Node.js no encontrado")
        return False
    
    # Verificar dependencias del frontend
    frontend_dir = Path("frontend")
    if not (frontend_dir / "node_modules").exists():
        print("📦 Instalando dependencias del frontend...")
        try:
            subprocess.run(['npm', 'install'], cwd=frontend_dir, check=True)
            print("✅ Dependencias del frontend instaladas")
        except subprocess.CalledProcessError:
            print("❌ Error instalando dependencias del frontend")
            return False
    else:
        print("✅ Dependencias del frontend ya instaladas")
    
    return True

def start_backend():
    """Inicia el servidor backend."""
    print("🚀 Iniciando backend en http://localhost:8000...")
    
    try:
        process = subprocess.Popen([
            sys.executable, '-m', 'uvicorn', 
            'app.main:app', 
            '--host', '0.0.0.0', 
            '--port', '8000', 
            '--reload'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Esperar a que el servidor esté listo
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ Backend iniciado correctamente")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Error iniciando backend:")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Error iniciando backend: {e}")
        return None

def start_frontend():
    """Inicia el servidor frontend."""
    print("🎨 Iniciando frontend en http://localhost:3000...")
    
    frontend_dir = Path("frontend")
    
    try:
        process = subprocess.Popen([
            'npm', 'run', 'dev'
        ], cwd=frontend_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Esperar a que el servidor esté listo
        time.sleep(5)
        
        if process.poll() is None:
            print("✅ Frontend iniciado correctamente")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Error iniciando frontend:")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Error iniciando frontend: {e}")
        return None

def open_browser():
    """Abre el navegador con la aplicación."""
    print("🌐 Abriendo navegador...")
    try:
        # Intentar abrir en el puerto 3000, si no funciona, probar 3001
        webbrowser.open('http://localhost:3000')
        time.sleep(2)
        # Si el puerto 3000 no funciona, Vite automáticamente usa 3001
        webbrowser.open('http://localhost:3001')
    except Exception as e:
        print(f"⚠️  No se pudo abrir el navegador automáticamente: {e}")
        print("📱 Abre manualmente: http://localhost:3000 o http://localhost:3001")

def cleanup_processes(backend_process, frontend_process):
    """Limpia los procesos al salir."""
    print("\n🛑 Cerrando aplicación...")
    
    if backend_process and backend_process.poll() is None:
        print("⏹️  Cerrando backend...")
        backend_process.terminate()
        try:
            backend_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            backend_process.kill()
    
    if frontend_process and frontend_process.poll() is None:
        print("⏹️  Cerrando frontend...")
        frontend_process.terminate()
        try:
            frontend_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            frontend_process.kill()
    
    print("✅ Aplicación cerrada correctamente")

def main():
    """Función principal."""
    print_banner()
    
    # Verificar dependencias
    if not check_dependencies():
        print("\n❌ No se pueden iniciar los servicios debido a dependencias faltantes")
        sys.exit(1)
    
    backend_process = None
    frontend_process = None
    
    try:
        # Iniciar backend
        backend_process = start_backend()
        if not backend_process:
            print("❌ No se pudo iniciar el backend")
            sys.exit(1)
        
        # Iniciar frontend
        frontend_process = start_frontend()
        if not frontend_process:
            print("❌ No se pudo iniciar el frontend")
            cleanup_processes(backend_process, None)
            sys.exit(1)
        
        # Abrir navegador
        time.sleep(2)
        open_browser()
        
        # Mostrar información
        print("\n" + "="*60)
        print("🎉 ¡Aplicación iniciada correctamente!")
        print("="*60)
        print("📊 Backend API:     http://localhost:8000")
        print("📚 Documentación:   http://localhost:8000/docs")
        print("🎨 Frontend:        http://localhost:3000 o http://localhost:3001")
        print("="*60)
        print("💡 Presiona Ctrl+C para cerrar la aplicación")
        print("="*60)
        
        # Mantener la aplicación corriendo
        while True:
            time.sleep(1)
            
            # Verificar que los procesos sigan corriendo
            if backend_process.poll() is not None:
                print("❌ El backend se ha cerrado inesperadamente")
                break
                
            if frontend_process.poll() is not None:
                print("❌ El frontend se ha cerrado inesperadamente")
                break
    
    except KeyboardInterrupt:
        print("\n👋 Cerrando aplicación...")
    
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
    
    finally:
        cleanup_processes(backend_process, frontend_process)

if __name__ == "__main__":
    main()
