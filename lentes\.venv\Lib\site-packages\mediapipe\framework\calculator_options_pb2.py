# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/calculator_options.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,mediapipe/framework/calculator_options.proto\x12\tmediapipe\"9\n\x11\x43\x61lculatorOptions\x12\x18\n\x0cmerge_fields\x18\x01 \x01(\x08\x42\x02\x18\x01*\n\x08\xa0\x9c\x01\x10\x80\x80\x80\x80\x02\x42\x34\n\x1a\x63om.google.mediapipe.protoB\x16\x43\x61lculatorOptionsProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.calculator_options_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\032com.google.mediapipe.protoB\026CalculatorOptionsProto'
  _CALCULATOROPTIONS.fields_by_name['merge_fields']._options = None
  _CALCULATOROPTIONS.fields_by_name['merge_fields']._serialized_options = b'\030\001'
  _globals['_CALCULATOROPTIONS']._serialized_start=59
  _globals['_CALCULATOROPTIONS']._serialized_end=116
# @@protoc_insertion_point(module_scope)
