# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/gpu/gpu_origin.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1emediapipe/gpu/gpu_origin.proto\x12\tmediapipe\"@\n\tGpuOrigin\"3\n\x04Mode\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x10\n\x0c\x43ONVENTIONAL\x10\x01\x12\x0c\n\x08TOP_LEFT\x10\x02\x42*\n\x18\x63om.google.mediapipe.gpuB\x0eGpuOriginProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.gpu.gpu_origin_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030com.google.mediapipe.gpuB\016GpuOriginProto'
  _globals['_GPUORIGIN']._serialized_start=45
  _globals['_GPUORIGIN']._serialized_end=109
  _globals['_GPUORIGIN_MODE']._serialized_start=58
  _globals['_GPUORIGIN_MODE']._serialized_end=109
# @@protoc_insertion_point(module_scope)
