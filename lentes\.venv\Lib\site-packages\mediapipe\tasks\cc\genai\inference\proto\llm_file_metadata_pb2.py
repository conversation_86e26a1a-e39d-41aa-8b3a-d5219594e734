# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/genai/inference/proto/llm_file_metadata.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.tasks.cc.genai.inference.proto import llm_params_pb2 as mediapipe_dot_tasks_dot_cc_dot_genai_dot_inference_dot_proto_dot_llm__params__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n@mediapipe/tasks/cc/genai/inference/proto/llm_file_metadata.proto\x12\x10odml.infra.proto\x1a\x39mediapipe/tasks/cc/genai/inference/proto/llm_params.proto\"\xe8\x02\n\x0fLlmFileMetadata\x12=\n\x07tensors\x18\x01 \x03(\x0b\x32,.odml.infra.proto.LlmFileMetadata.TensorInfo\x12\x35\n\x0cmodel_params\x18\x02 \x01(\x0b\x32\x1f.odml.infra.proto.LlmParameters\x12\x11\n\tlora_rank\x18\x03 \x01(\x05\x1a\xcb\x01\n\nTensorInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06offset\x18\x02 \x01(\x04\x12\x0c\n\x04size\x18\x03 \x01(\x04\x12H\n\tdata_type\x18\x04 \x01(\x0e\x32\x35.odml.infra.proto.LlmFileMetadata.TensorInfo.DataType\"G\n\x08\x44\x61taType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x0b\n\x07\x46LOAT32\x10\x01\x12\x08\n\x04INT8\x10\x02\x12\x08\n\x04INT4\x10\x03\x12\t\n\x05UINT4\x10\x04\x42\x33\n\x1b\x63om.google.odml.infra.protoB\x14LlmFileMetadataProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.genai.inference.proto.llm_file_metadata_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\033com.google.odml.infra.protoB\024LlmFileMetadataProto'
  _globals['_LLMFILEMETADATA']._serialized_start=146
  _globals['_LLMFILEMETADATA']._serialized_end=506
  _globals['_LLMFILEMETADATA_TENSORINFO']._serialized_start=303
  _globals['_LLMFILEMETADATA_TENSORINFO']._serialized_end=506
  _globals['_LLMFILEMETADATA_TENSORINFO_DATATYPE']._serialized_start=435
  _globals['_LLMFILEMETADATA_TENSORINFO_DATATYPE']._serialized_end=506
# @@protoc_insertion_point(module_scope)
