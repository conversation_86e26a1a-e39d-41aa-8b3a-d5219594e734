# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/genai/inference/proto/prompt_template.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n>mediapipe/tasks/cc/genai/inference/proto/prompt_template.proto\x12\x10odml.infra.proto\"V\n\x0ePromptTemplate\x12\x16\n\x0esession_prefix\x18\x01 \x01(\t\x12\x15\n\rprompt_prefix\x18\x02 \x01(\t\x12\x15\n\rprompt_suffix\x18\x03 \x01(\tB2\n\x1b\x63om.google.odml.infra.protoB\x13PromptTemplateProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.genai.inference.proto.prompt_template_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\033com.google.odml.infra.protoB\023PromptTemplateProto'
  _globals['_PROMPTTEMPLATE']._serialized_start=84
  _globals['_PROMPTTEMPLATE']._serialized_end=170
# @@protoc_insertion_point(module_scope)
