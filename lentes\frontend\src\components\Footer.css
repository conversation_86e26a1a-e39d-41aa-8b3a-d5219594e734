/* <PERSON><PERSON><PERSON><PERSON> del Footer */

.footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  margin-top: auto;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 3rem 2rem 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  color: #3498db;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
  display: inline-block;
}

/* Logo y descripción */
.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.footer-logo .logo-icon {
  font-size: 2rem;
}

.footer-logo h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(45deg, #3498db, #2ecc71);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-description {
  line-height: 1.6;
  margin-bottom: 1.5rem;
  color: #bdc3c7;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(52, 152, 219, 0.2);
  border-radius: 50%;
  text-decoration: none;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: #3498db;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

/* Enlaces */
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  padding: 0.25rem 0;
}

.footer-links a:hover {
  color: #3498db;
  padding-left: 0.5rem;
}

/* Información de contacto */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #bdc3c7;
}

.contact-icon {
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

/* Newsletter */
.newsletter-text {
  color: #bdc3c7;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.newsletter-form {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.newsletter-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #34495e;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.9rem;
}

.newsletter-input::placeholder {
  color: #bdc3c7;
}

.newsletter-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.newsletter-btn {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  border: none;
  border-radius: 5px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.newsletter-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

.newsletter-benefits {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #bdc3c7;
  font-size: 0.9rem;
}

.benefit-icon {
  font-size: 1rem;
}

/* Certificaciones */
.footer-certifications {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 2rem;
  padding: 2rem 0;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin-bottom: 2rem;
}

.cert-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
  min-width: 100px;
}

.cert-icon {
  font-size: 2rem;
  color: #3498db;
}

.cert-item span:last-child {
  font-size: 0.9rem;
  font-weight: 500;
  color: #bdc3c7;
}

/* Línea divisoria */
.footer-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, #34495e, transparent);
  margin: 2rem 0;
}

/* Footer bottom */
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #34495e;
}

.copyright {
  flex: 1;
}

.copyright p {
  margin: 0;
  color: #bdc3c7;
  font-size: 0.9rem;
}

.tech-credit {
  font-size: 0.8rem !important;
  color: #95a5a6 !important;
  font-style: italic;
}

.legal-links {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.legal-links button {
  background: none;
  border: none;
  color: #bdc3c7;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  cursor: pointer;
  padding: 0;
  font-family: inherit;
}

.legal-links button:hover {
  color: #3498db;
}

/* Responsive design */
@media (max-width: 1024px) {
  .footer-certifications {
    gap: 1.5rem;
  }
  
  .cert-item {
    min-width: 80px;
  }
  
  .cert-icon {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .footer-container {
    padding: 2rem 1rem 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .footer-certifications {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .legal-links {
    justify-content: center;
  }
  
  .newsletter-form {
    flex-direction: column;
  }
  
  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-certifications {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .legal-links {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .cert-item span:last-child {
    font-size: 0.8rem;
  }
}
