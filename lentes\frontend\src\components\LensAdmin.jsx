import React, { useState, useRef } from 'react'
import './LensAdmin.css'

const LensAdmin = () => {
  const [selectedFile, setSelectedFile] = useState(null)
  const [lensType, setLensType] = useState('glasses')
  const [lensColor, setLensColor] = useState('black')
  const [customColor, setCustomColor] = useState('')
  const [uploading, setUploading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState('') // 'success' or 'error'
  const fileInputRef = useRef(null)

  const lensTypes = [
    { value: 'glasses', label: 'Lentes Normales' },
    { value: 'sunglasses', label: 'Lentes de Sol' },
    { value: 'reading_glasses', label: 'Lentes de Lectura' },
    { value: 'safety_glasses', label: 'Lentes de Seguridad' },
    { value: 'fashion_glasses', label: 'Lentes de Moda' }
  ]

  const predefinedColors = [
    'black', 'blue', 'brown', 'red', 'green', 'gray', 'gold', 'silver'
  ]

  const handleFileSelect = (event) => {
    const file = event.target.files[0]
    if (file) {
      // Validar que sea una imagen PNG
      if (file.type !== 'image/png') {
        setMessage('Por favor selecciona un archivo PNG')
        setMessageType('error')
        return
      }
      
      // Validar tamaño (máximo 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setMessage('El archivo es demasiado grande. Máximo 5MB.')
        setMessageType('error')
        return
      }

      setSelectedFile(file)
      setMessage('')
    }
  }

  const handleDrop = (event) => {
    event.preventDefault()
    const file = event.dataTransfer.files[0]
    if (file) {
      const fakeEvent = { target: { files: [file] } }
      handleFileSelect(fakeEvent)
    }
  }

  const handleDragOver = (event) => {
    event.preventDefault()
  }

  const handleUpload = async () => {
    if (!selectedFile) {
      setMessage('Por favor selecciona un archivo')
      setMessageType('error')
      return
    }

    const finalColor = lensColor === 'custom' ? customColor : lensColor
    
    if (!finalColor.trim()) {
      setMessage('Por favor especifica un color')
      setMessageType('error')
      return
    }

    try {
      setUploading(true)
      setMessage('')

      // Crear nombre del archivo
      const fileName = `${lensType}_${finalColor.toLowerCase().replace(/\s+/g, '_')}.png`
      
      // Crear FormData
      const formData = new FormData()
      formData.append('file', selectedFile, fileName)
      formData.append('lens_type', lensType)
      formData.append('lens_color', finalColor)

      // Simular upload (aquí iría la llamada real al backend)
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setMessage(`Lente "${fileName}" subido exitosamente`)
      setMessageType('success')
      
      // Limpiar formulario
      setSelectedFile(null)
      setCustomColor('')
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }

    } catch (error) {
      console.error('Error subiendo archivo:', error)
      setMessage('Error al subir el archivo')
      setMessageType('error')
    } finally {
      setUploading(false)
    }
  }

  const clearFile = () => {
    setSelectedFile(null)
    setMessage('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="lens-admin">
      <div className="admin-container">
        <div className="admin-header">
          <h1>
            <span className="admin-icon">⚙️</span>
            Administración de Lentes
          </h1>
          <p>Sube nuevos modelos de lentes para la galería virtual</p>
        </div>

        <div className="upload-section">
          <div className="upload-form">
            <div className="form-group">
              <label htmlFor="lens-type">Tipo de Lente</label>
              <select
                id="lens-type"
                value={lensType}
                onChange={(e) => setLensType(e.target.value)}
                className="form-select"
              >
                {lensTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="lens-color">Color del Lente</label>
              <select
                id="lens-color"
                value={lensColor}
                onChange={(e) => setLensColor(e.target.value)}
                className="form-select"
              >
                {predefinedColors.map(color => (
                  <option key={color} value={color}>
                    {color.charAt(0).toUpperCase() + color.slice(1)}
                  </option>
                ))}
                <option value="custom">Color Personalizado</option>
              </select>
            </div>

            {lensColor === 'custom' && (
              <div className="form-group">
                <label htmlFor="custom-color">Color Personalizado</label>
                <input
                  id="custom-color"
                  type="text"
                  value={customColor}
                  onChange={(e) => setCustomColor(e.target.value)}
                  placeholder="Ej: azul_marino, rojo_brillante"
                  className="form-input"
                />
              </div>
            )}

            <div className="form-group">
              <label>Archivo de Imagen (PNG)</label>
              <div
                className={`file-drop-zone ${selectedFile ? 'has-file' : ''}`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() => fileInputRef.current?.click()}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".png"
                  onChange={handleFileSelect}
                  style={{ display: 'none' }}
                />
                
                {selectedFile ? (
                  <div className="file-info">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <polyline points="10,9 9,9 8,9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <div className="file-details">
                      <h4>{selectedFile.name}</h4>
                      <p>{(selectedFile.size / 1024).toFixed(1)} KB</p>
                    </div>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation()
                        clearFile()
                      }}
                      className="remove-file-btn"
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </button>
                  </div>
                ) : (
                  <div className="drop-placeholder">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <polyline points="7,10 12,15 17,10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <h3>Arrastra tu archivo aquí</h3>
                    <p>o haz clic para seleccionar</p>
                    <small>Solo archivos PNG, máximo 5MB</small>
                  </div>
                )}
              </div>
            </div>

            {message && (
              <div className={`message ${messageType}`}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  {messageType === 'success' ? (
                    <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  ) : (
                    <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z" fill="currentColor"/>
                  )}
                </svg>
                {message}
              </div>
            )}

            <div className="form-actions">
              <button
                onClick={handleUpload}
                disabled={!selectedFile || uploading}
                className="upload-btn"
              >
                {uploading ? (
                  <>
                    <div className="spinner-small"></div>
                    Subiendo...
                  </>
                ) : (
                  <>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <polyline points="17,8 12,3 7,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <line x1="12" y1="3" x2="12" y2="15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    Subir Lente
                  </>
                )}
              </button>
            </div>
          </div>

          <div className="upload-preview">
            <h3>Vista Previa</h3>
            <div className="preview-card">
              <div className="preview-header">
                <h4>
                  {lensTypes.find(t => t.value === lensType)?.label}
                </h4>
                <span className="preview-color">
                  {lensColor === 'custom' ? customColor || 'Color personalizado' : lensColor}
                </span>
              </div>
              <div className="preview-image">
                {selectedFile ? (
                  <img
                    src={URL.createObjectURL(selectedFile)}
                    alt="Vista previa"
                    onLoad={(e) => URL.revokeObjectURL(e.target.src)}
                  />
                ) : (
                  <div className="preview-placeholder">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" fill="currentColor"/>
                    </svg>
                    <p>Selecciona una imagen para ver la vista previa</p>
                  </div>
                )}
              </div>
              <div className="preview-filename">
                {selectedFile ? 
                  `${lensType}_${lensColor === 'custom' ? customColor.toLowerCase().replace(/\s+/g, '_') : lensColor}.png` :
                  'nombre_archivo.png'
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LensAdmin
