# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/util/tracking/box_detector.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.util.tracking import box_tracker_pb2 as mediapipe_dot_util_dot_tracking_dot_box__tracker__pb2
from mediapipe.util.tracking import region_flow_pb2 as mediapipe_dot_util_dot_tracking_dot_region__flow__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*mediapipe/util/tracking/box_detector.proto\x12\tmediapipe\x1a)mediapipe/util/tracking/box_tracker.proto\x1a)mediapipe/util/tracking/region_flow.proto\"\xec\x04\n\x12\x42oxDetectorOptions\x12\x46\n\nindex_type\x18\x01 \x01(\x0e\x32\'.mediapipe.BoxDetectorOptions.IndexType:\tOPENCV_BF\x12\x1f\n\x14\x64\x65tect_every_n_frame\x18\x02 \x01(\x05:\x01\x30\x12 \n\x11\x64\x65tect_out_of_fov\x18\x04 \x01(\x08:\x05\x66\x61lse\x12N\n\x14image_query_settings\x18\x03 \x01(\x0b\x32\x30.mediapipe.BoxDetectorOptions.ImageQuerySettings\x12\x1b\n\x0f\x64\x65scriptor_dims\x18\x05 \x01(\x05:\x02\x34\x30\x12!\n\x16min_num_correspondence\x18\x06 \x01(\x05:\x01\x35\x12,\n\x1dransac_reprojection_threshold\x18\x07 \x01(\x02:\x05\x30.005\x12\x1f\n\x12max_match_distance\x18\x08 \x01(\x02:\x03\x30.9\x12#\n\x16max_perspective_factor\x18\t \x01(\x02:\x03\x30.1\x1a\x93\x01\n\x12ImageQuerySettings\x12 \n\x13pyramid_bottom_size\x18\x01 \x01(\x05:\x03\x36\x34\x30\x12!\n\x14pyramid_scale_factor\x18\x02 \x01(\x02:\x03\x31.2\x12\x1d\n\x12max_pyramid_levels\x18\x03 \x01(\x05:\x01\x34\x12\x19\n\x0cmax_features\x18\x04 \x01(\x05:\x03\x35\x30\x30\"1\n\tIndexType\x12\x15\n\x11INDEX_UNSPECIFIED\x10\x00\x12\r\n\tOPENCV_BF\x10\x01\"\x9f\x02\n\x10\x42oxDetectorIndex\x12\x37\n\tbox_entry\x18\x01 \x03(\x0b\x32$.mediapipe.BoxDetectorIndex.BoxEntry\x1a\xd1\x01\n\x08\x42oxEntry\x12\x44\n\x0b\x66rame_entry\x18\x01 \x03(\x0b\x32/.mediapipe.BoxDetectorIndex.BoxEntry.FrameEntry\x1a\x7f\n\nFrameEntry\x12%\n\x03\x62ox\x18\x01 \x01(\x0b\x32\x18.mediapipe.TimedBoxProto\x12\x11\n\tkeypoints\x18\x02 \x03(\x02\x12\x37\n\x0b\x64\x65scriptors\x18\x03 \x03(\x0b\x32\".mediapipe.BinaryFeatureDescriptorB1\n\x1d\x63om.google.mediapipe.trackingB\x10\x42oxDetectorProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.util.tracking.box_detector_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\035com.google.mediapipe.trackingB\020BoxDetectorProto'
  _globals['_BOXDETECTOROPTIONS']._serialized_start=144
  _globals['_BOXDETECTOROPTIONS']._serialized_end=764
  _globals['_BOXDETECTOROPTIONS_IMAGEQUERYSETTINGS']._serialized_start=566
  _globals['_BOXDETECTOROPTIONS_IMAGEQUERYSETTINGS']._serialized_end=713
  _globals['_BOXDETECTOROPTIONS_INDEXTYPE']._serialized_start=715
  _globals['_BOXDETECTOROPTIONS_INDEXTYPE']._serialized_end=764
  _globals['_BOXDETECTORINDEX']._serialized_start=767
  _globals['_BOXDETECTORINDEX']._serialized_end=1054
  _globals['_BOXDETECTORINDEX_BOXENTRY']._serialized_start=845
  _globals['_BOXDETECTORINDEX_BOXENTRY']._serialized_end=1054
  _globals['_BOXDETECTORINDEX_BOXENTRY_FRAMEENTRY']._serialized_start=927
  _globals['_BOXDETECTORINDEX_BOXENTRY_FRAMEENTRY']._serialized_end=1054
# @@protoc_insertion_point(module_scope)
