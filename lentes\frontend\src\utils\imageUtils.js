/**
 * Utilidades para manejo de imágenes
 */

/**
 * Valida si un archivo es una imagen válida
 * @param {File} file - Archivo a validar
 * @returns {Object} - Resultado de la validación
 */
export const validateImageFile = (file) => {
  const result = {
    isValid: true,
    error: null,
  }

  // Verificar que sea un archivo
  if (!file) {
    result.isValid = false
    result.error = 'No se ha seleccionado ningún archivo'
    return result
  }

  // Verificar tipo de archivo
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    result.isValid = false
    result.error = 'Tipo de archivo no válido. Solo se permiten: JPEG, PNG, WebP'
    return result
  }

  // Verificar tamaño (máximo 10MB)
  const maxSize = 10 * 1024 * 1024 // 10MB en bytes
  if (file.size > maxSize) {
    result.isValid = false
    result.error = 'El archivo es demasiado grande. Tamaño máximo: 10MB'
    return result
  }

  return result
}

/**
 * Redimensiona una imagen manteniendo la proporción
 * @param {File} file - Archivo de imagen
 * @param {number} maxWidth - Ancho máximo
 * @param {number} maxHeight - Alto máximo
 * @param {number} quality - Calidad de compresión (0-1)
 * @returns {Promise<Blob>} - Imagen redimensionada
 */
export const resizeImage = (file, maxWidth = 800, maxHeight = 600, quality = 0.8) => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // Calcular nuevas dimensiones manteniendo proporción
      let { width, height } = img
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }
      }

      // Configurar canvas
      canvas.width = width
      canvas.height = height

      // Dibujar imagen redimensionada
      ctx.drawImage(img, 0, 0, width, height)

      // Convertir a blob
      canvas.toBlob(resolve, file.type, quality)
    }

    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Convierte un blob a URL de objeto
 * @param {Blob} blob - Blob a convertir
 * @returns {string} - URL del objeto
 */
export const blobToObjectURL = (blob) => {
  return URL.createObjectURL(blob)
}

/**
 * Descarga una imagen desde una URL
 * @param {string} url - URL de la imagen
 * @param {string} filename - Nombre del archivo
 */
export const downloadImage = (url, filename = 'imagen.png') => {
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * Obtiene información de una imagen
 * @param {File} file - Archivo de imagen
 * @returns {Promise<Object>} - Información de la imagen
 */
export const getImageInfo = (file) => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
        size: file.size,
        type: file.type,
        name: file.name,
        aspectRatio: img.naturalWidth / img.naturalHeight,
      })
    }

    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Formatea el tamaño de archivo en formato legible
 * @param {number} bytes - Tamaño en bytes
 * @returns {string} - Tamaño formateado
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Crea una vista previa de imagen
 * @param {File} file - Archivo de imagen
 * @returns {Promise<string>} - URL de vista previa
 */
export const createImagePreview = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}
