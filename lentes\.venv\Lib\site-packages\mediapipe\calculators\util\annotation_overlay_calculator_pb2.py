# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/util/annotation_overlay_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2
from mediapipe.util import color_pb2 as mediapipe_dot_util_dot_color__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n>mediapipe/calculators/util/annotation_overlay_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\x1a\x1amediapipe/util/color.proto\"\xd2\x02\n\"AnnotationOverlayCalculatorOptions\x12\x1d\n\x0f\x63\x61nvas_width_px\x18\x02 \x01(\x05:\x04\x31\x39\x32\x30\x12\x1e\n\x10\x63\x61nvas_height_px\x18\x03 \x01(\x05:\x04\x31\x30\x38\x30\x12&\n\x0c\x63\x61nvas_color\x18\x04 \x01(\x0b\x32\x10.mediapipe.Color\x12#\n\x14\x66lip_text_vertically\x18\x05 \x01(\x08:\x05\x66\x61lse\x12&\n\x18gpu_uses_top_left_origin\x18\x06 \x01(\x08:\x04true\x12\x1b\n\x10gpu_scale_factor\x18\x07 \x01(\x02:\x01\x31\x32[\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\x87\xf0\xbfw \x01(\x0b\x32-.mediapipe.AnnotationOverlayCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.util.annotation_overlay_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_ANNOTATIONOVERLAYCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_ANNOTATIONOVERLAYCALCULATOROPTIONS']._serialized_start=144
  _globals['_ANNOTATIONOVERLAYCALCULATOROPTIONS']._serialized_end=482
# @@protoc_insertion_point(module_scope)
