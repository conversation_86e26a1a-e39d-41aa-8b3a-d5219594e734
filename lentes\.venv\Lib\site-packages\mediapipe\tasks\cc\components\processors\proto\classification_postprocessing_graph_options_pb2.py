# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/components/processors/proto/classification_postprocessing_graph_options.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.calculators.tensor import tensors_to_classification_calculator_pb2 as mediapipe_dot_calculators_dot_tensor_dot_tensors__to__classification__calculator__pb2
from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2
from mediapipe.tasks.cc.components.calculators import classification_aggregation_calculator_pb2 as mediapipe_dot_tasks_dot_cc_dot_components_dot_calculators_dot_classification__aggregation__calculator__pb2
from mediapipe.tasks.cc.components.calculators import score_calibration_calculator_pb2 as mediapipe_dot_tasks_dot_cc_dot_components_dot_calculators_dot_score__calibration__calculator__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n`mediapipe/tasks/cc/components/processors/proto/classification_postprocessing_graph_options.proto\x12+mediapipe.tasks.components.processors.proto\x1aGmediapipe/calculators/tensor/tensors_to_classification_calculator.proto\x1a$mediapipe/framework/calculator.proto\x1aUmediapipe/tasks/cc/components/calculators/classification_aggregation_calculator.proto\x1aLmediapipe/tasks/cc/components/calculators/score_calibration_calculator.proto\"\xa0\x05\n(ClassificationPostprocessingGraphOptions\x12\x95\x01\n\x19score_calibration_options\x18\x04 \x03(\x0b\x32r.mediapipe.tasks.components.processors.proto.ClassificationPostprocessingGraphOptions.ScoreCalibrationOptionsEntry\x12_\n\"tensors_to_classifications_options\x18\x01 \x03(\x0b\x32\x33.mediapipe.TensorsToClassificationCalculatorOptions\x12\x61\n\"classification_aggregation_options\x18\x02 \x01(\x0b\x32\x35.mediapipe.ClassificationAggregationCalculatorOptions\x12\x1d\n\x15has_quantized_outputs\x18\x03 \x01(\x08\x1ar\n\x1cScoreCalibrationOptionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x41\n\x05value\x18\x02 \x01(\x0b\x32\x32.mediapipe.tasks.ScoreCalibrationCalculatorOptions:\x02\x38\x01\x32\x84\x01\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xb6\xcf\xc5\xdb\x01 \x01(\x0b\x32U.mediapipe.tasks.components.processors.proto.ClassificationPostprocessingGraphOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.components.processors.proto.classification_postprocessing_graph_options_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_CLASSIFICATIONPOSTPROCESSINGGRAPHOPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _CLASSIFICATIONPOSTPROCESSINGGRAPHOPTIONS_SCORECALIBRATIONOPTIONSENTRY._options = None
  _CLASSIFICATIONPOSTPROCESSINGGRAPHOPTIONS_SCORECALIBRATIONOPTIONSENTRY._serialized_options = b'8\001'
  _globals['_CLASSIFICATIONPOSTPROCESSINGGRAPHOPTIONS']._serialized_start=422
  _globals['_CLASSIFICATIONPOSTPROCESSINGGRAPHOPTIONS']._serialized_end=1094
  _globals['_CLASSIFICATIONPOSTPROCESSINGGRAPHOPTIONS_SCORECALIBRATIONOPTIONSENTRY']._serialized_start=845
  _globals['_CLASSIFICATIONPOSTPROCESSINGGRAPHOPTIONS_SCORECALIBRATIONOPTIONSENTRY']._serialized_end=959
# @@protoc_insertion_point(module_scope)
