#!/usr/bin/env python3
"""
Script de demostración para el sistema de pedidos y envíos.

Este script crea pedidos de ejemplo y simula su progreso.
"""

import requests
import json
import time
from datetime import datetime

# Configuración
BASE_URL = "http://localhost:8000/api/v1"

def create_demo_order():
    """Crea un pedido de demostración."""
    order_data = {
        "items": [
            {
                "product_id": 1,
                "product_name": "Ray-Ban Aviator Classic",
                "brand": "Ray-Ban",
                "selected_color": "black",
                "quantity": 1,
                "unit_price": 159.99,
                "total_price": 159.99
            },
            {
                "product_id": 3,
                "product_name": "<PERSON><PERSON>",
                "brand": "Warby Parker",
                "selected_color": "brown",
                "quantity": 2,
                "unit_price": 95.00,
                "total_price": 190.00
            }
        ],
        "shipping_address": {
            "name": "<PERSON>",
            "email": "<EMAIL>",
            "phone": "******-123-4567",
            "address": "123 Calle Principal, Apt 4B",
            "city": "Ciudad de México",
            "state": "CDMX",
            "zip_code": "01000",
            "country": "MX"
        },
        "shipping_method": "express",
        "totals": {
            "subtotal": 349.99,
            "shipping_cost": 19.99,
            "tax": 28.00,
            "discount": 0,
            "total": 397.98
        },
        "notes": "Pedido de demostración - OpticaVirtual"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/orders/create", json=order_data)
        response.raise_for_status()
        
        result = response.json()
        if result["success"]:
            print(f"✅ Pedido creado exitosamente: {result['order']['order_number']}")
            print(f"📅 Entrega estimada: {result['estimated_delivery']}")
            return result['order']['order_number']
        else:
            print(f"❌ Error creando pedido: {result.get('message', 'Error desconocido')}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error de conexión: {e}")
        return None

def track_order(order_number):
    """Obtiene información de seguimiento de un pedido."""
    try:
        response = requests.get(f"{BASE_URL}/orders/{order_number}/tracking")
        response.raise_for_status()
        
        result = response.json()
        if result["success"]:
            print(f"\n📦 Seguimiento del pedido: {order_number}")
            print(f"📊 Estado actual: {result['current_status'].upper()}")
            
            if result.get('tracking_number'):
                print(f"🔢 Número de seguimiento: {result['tracking_number']}")
            
            if result.get('estimated_delivery'):
                print(f"📅 Entrega estimada: {result['estimated_delivery']}")
            
            print("\n📋 Historial de eventos:")
            for event in result.get('tracking_events', []):
                timestamp = datetime.fromisoformat(event['timestamp'].replace('Z', '+00:00'))
                print(f"  • {timestamp.strftime('%Y-%m-%d %H:%M')} - {event['description']}")
                if event.get('location'):
                    print(f"    📍 {event['location']}")
                if event.get('carrier'):
                    print(f"    🚛 {event['carrier']}")
                print()
        else:
            print(f"❌ Error obteniendo seguimiento: {result.get('message', 'Error desconocido')}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error de conexión: {e}")

def simulate_order_progress(order_number):
    """Simula el progreso de un pedido."""
    try:
        response = requests.post(f"{BASE_URL}/orders/{order_number}/simulate-progress")
        response.raise_for_status()
        
        result = response.json()
        if result["success"]:
            print(f"🔄 Progreso simulado: {result['message']}")
            print(f"📊 Nuevo estado: {result['new_status'].upper()}")
        else:
            print(f"❌ Error simulando progreso: {result.get('message', 'Error desconocido')}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error de conexión: {e}")

def get_all_orders():
    """Obtiene lista de todos los pedidos."""
    try:
        response = requests.get(f"{BASE_URL}/orders/")
        response.raise_for_status()
        
        result = response.json()
        if result["success"]:
            print(f"\n📋 Total de pedidos: {result['total_count']}")
            print("=" * 50)
            
            for order in result['orders']:
                print(f"🆔 Pedido: {order['order_number']}")
                print(f"📊 Estado: {order['status'].upper()}")
                print(f"💰 Total: ${order['totals']['total']:.2f}")
                print(f"📅 Creado: {order['created_at']}")
                print("-" * 30)
        else:
            print(f"❌ Error obteniendo pedidos: {result.get('message', 'Error desconocido')}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error de conexión: {e}")

def main():
    """Función principal de demostración."""
    print("🕶️ DEMOSTRACIÓN DEL SISTEMA DE PEDIDOS - OPTICAVIRTUAL")
    print("=" * 60)
    
    # Verificar que el servidor esté funcionando
    try:
        response = requests.get(f"{BASE_URL}/orders/status/options")
        response.raise_for_status()
        print("✅ Servidor backend conectado correctamente")
    except requests.exceptions.RequestException:
        print("❌ Error: No se puede conectar al servidor backend")
        print("💡 Asegúrate de que el servidor esté ejecutándose en http://localhost:8000")
        return
    
    print("\n1️⃣ Creando pedido de demostración...")
    order_number = create_demo_order()
    
    if not order_number:
        print("❌ No se pudo crear el pedido. Terminando demostración.")
        return
    
    print("\n2️⃣ Obteniendo seguimiento inicial...")
    track_order(order_number)
    
    print("\n3️⃣ Simulando progreso del pedido...")
    for i in range(3):
        print(f"\n🔄 Simulación {i+1}/3...")
        simulate_order_progress(order_number)
        time.sleep(1)
        track_order(order_number)
        time.sleep(2)
    
    print("\n4️⃣ Lista de todos los pedidos:")
    get_all_orders()
    
    print("\n🎉 ¡Demostración completada!")
    print("\n💡 Puedes usar estos números de pedido en la interfaz web:")
    print(f"   • Número de pedido: {order_number}")
    print("   • Ve a la sección 'Seguimiento' en http://localhost:3001")

if __name__ == "__main__":
    main()
