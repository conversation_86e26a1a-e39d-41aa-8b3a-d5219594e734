import React, { useState, useEffect } from 'react'
import './LensGallery.css'

const LensGallery = ({ isOpen, onClose, lensType, onLensSelect, currentLensColor }) => {
  const [gallery, setGallery] = useState({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Cargar galería de lentes cuando se abre el modal
  useEffect(() => {
    if (isOpen) {
      loadLensGallery()
    }
  }, [isOpen])

  const loadLensGallery = async () => {
    try {
      setLoading(true)
      setError('')
      
      const response = await fetch('http://localhost:8000/api/v1/lens/lens-gallery')
      const data = await response.json()
      
      if (data.success) {
        setGallery(data.gallery)
      } else {
        setError(data.error || 'Error cargando galería')
      }
    } catch (err) {
      console.error('Error cargando galería:', err)
      setError('Error de conexión al cargar la galería')
    } finally {
      setLoading(false)
    }
  }

  const handleLensSelect = (type, color) => {
    if (onLensSelect) {
      onLensSelect(type, color)
    }
    onClose()
  }

  const getLensTypeTitle = (type) => {
    const titles = {
      'glasses': 'Lentes Normales',
      'sunglasses': 'Lentes de Sol',
      'reading_glasses': 'Lentes de Lectura',
      'safety_glasses': 'Lentes de Seguridad',
      'fashion_glasses': 'Lentes de Moda'
    }
    return titles[type] || type
  }

  const getColorName = (color) => {
    const colorNames = {
      'black': 'Negro',
      'blue': 'Azul',
      'brown': 'Marrón',
      'red': 'Rojo',
      'green': 'Verde',
      'gray': 'Gris',
      'gold': 'Dorado',
      'silver': 'Plateado'
    }
    return colorNames[color] || color
  }

  if (!isOpen) return null

  return (
    <div className="lens-gallery-overlay">
      <div className="lens-gallery-modal">
        <div className="gallery-header">
          <h2>
            <span className="gallery-icon">👓</span>
            Galería de {getLensTypeTitle(lensType)}
          </h2>
          <button className="close-btn" onClick={onClose}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>

        <div className="gallery-content">
          {loading && (
            <div className="loading-state">
              <div className="spinner"></div>
              <p>Cargando modelos de lentes...</p>
            </div>
          )}

          {error && (
            <div className="error-state">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z" fill="currentColor"/>
              </svg>
              <p>{error}</p>
              <button onClick={loadLensGallery} className="retry-btn">
                Reintentar
              </button>
            </div>
          )}

          {!loading && !error && gallery[lensType] && (
            <div className="lens-grid">
              {Object.entries(gallery[lensType]).map(([color, lensInfo]) => (
                <div 
                  key={color}
                  className={`lens-card ${currentLensColor === color ? 'selected' : ''}`}
                  onClick={() => handleLensSelect(lensType, color)}
                >
                  <div className="lens-preview">
                    <img 
                      src={`http://localhost:8000${lensInfo.path}`}
                      alt={lensInfo.display_name}
                      onError={(e) => {
                        e.target.style.display = 'none'
                        e.target.nextSibling.style.display = 'flex'
                      }}
                    />
                    <div className="lens-placeholder" style={{ display: 'none' }}>
                      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" fill="currentColor"/>
                      </svg>
                    </div>
                  </div>
                  
                  <div className="lens-info">
                    <h4>{getColorName(color)}</h4>
                    <p className="lens-type">{getLensTypeTitle(lensType)}</p>
                    {currentLensColor === color && (
                      <div className="selected-badge">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        Seleccionado
                      </div>
                    )}
                  </div>
                  
                  <div className="lens-overlay">
                    <button className="try-btn">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 15.5c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z" fill="currentColor"/>
                        <path d="M9 2L7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2H9zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" fill="currentColor"/>
                      </svg>
                      Probar
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {!loading && !error && (!gallery[lensType] || Object.keys(gallery[lensType]).length === 0) && (
            <div className="empty-state">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" fill="currentColor"/>
              </svg>
              <h3>No hay modelos disponibles</h3>
              <p>No se encontraron modelos para {getLensTypeTitle(lensType)}</p>
            </div>
          )}
        </div>

        <div className="gallery-footer">
          <div className="gallery-stats">
            {gallery[lensType] && (
              <span>{Object.keys(gallery[lensType]).length} modelos disponibles</span>
            )}
          </div>
          <button onClick={onClose} className="close-gallery-btn">
            Cerrar Galería
          </button>
        </div>
      </div>
    </div>
  )
}

export default LensGallery
