#!/usr/bin/env python3
"""
Script para crear una imagen de prueba con un rostro simple.
"""

from PIL import Image, ImageDraw
import numpy as np
import cv2

def create_test_face():
    """Crea una imagen de prueba con un rostro simple."""
    # Crear imagen de 400x400 píxeles
    width, height = 400, 400
    img = Image.new('RGB', (width, height), 'lightblue')
    draw = ImageDraw.Draw(img)
    
    # Dibujar cara (círculo)
    face_center = (width // 2, height // 2)
    face_radius = 120
    draw.ellipse([face_center[0] - face_radius, face_center[1] - face_radius,
                  face_center[0] + face_radius, face_center[1] + face_radius],
                 fill='peachpuff', outline='black', width=2)
    
    # Dibujar ojos
    left_eye = (face_center[0] - 40, face_center[1] - 30)
    right_eye = (face_center[0] + 40, face_center[1] - 30)
    eye_radius = 15
    
    # Ojos
    draw.ellipse([left_eye[0] - eye_radius, left_eye[1] - eye_radius,
                  left_eye[0] + eye_radius, left_eye[1] + eye_radius],
                 fill='white', outline='black', width=2)
    
    draw.ellipse([right_eye[0] - eye_radius, right_eye[1] - eye_radius,
                  right_eye[0] + eye_radius, right_eye[1] + eye_radius],
                 fill='white', outline='black', width=2)
    
    # Pupilas
    draw.ellipse([left_eye[0] - 5, left_eye[1] - 5,
                  left_eye[0] + 5, left_eye[1] + 5],
                 fill='black')
    
    draw.ellipse([right_eye[0] - 5, right_eye[1] - 5,
                  right_eye[0] + 5, right_eye[1] + 5],
                 fill='black')
    
    # Nariz
    nose_tip = (face_center[0], face_center[1] + 10)
    draw.ellipse([nose_tip[0] - 8, nose_tip[1] - 15,
                  nose_tip[0] + 8, nose_tip[1] + 5],
                 fill='peachpuff', outline='black', width=1)
    
    # Boca
    mouth_center = (face_center[0], face_center[1] + 50)
    draw.arc([mouth_center[0] - 30, mouth_center[1] - 15,
              mouth_center[0] + 30, mouth_center[1] + 15],
             start=0, end=180, fill='black', width=3)
    
    # Guardar imagen
    img.save('test_face.jpg', 'JPEG')
    print("Imagen de prueba creada: test_face.jpg")

if __name__ == "__main__":
    create_test_face()
