import React, { useState, useEffect } from 'react'
import axios from 'axios'
import './OrderTracking.css'

const OrderTracking = () => {
  const [trackingNumber, setTrackingNumber] = useState('')
  const [orderNumber, setOrderNumber] = useState('')
  const [trackingData, setTrackingData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [searchType, setSearchType] = useState('order') // 'order' or 'tracking'

  const handleSearch = async () => {
    if (!trackingNumber.trim() && !orderNumber.trim()) {
      setError('Por favor ingresa un número de pedido o seguimiento')
      return
    }

    setLoading(true)
    setError('')
    setTrackingData(null)

    try {
      let response
      
      if (searchType === 'order' && orderNumber.trim()) {
        response = await axios.get(`/api/v1/orders/${orderNumber.trim()}/tracking`)
      } else if (searchType === 'tracking' && trackingNumber.trim()) {
        response = await axios.get(`/api/v1/orders/tracking/${trackingNumber.trim()}`)
      } else {
        throw new Error('Tipo de búsqueda no válido')
      }

      setTrackingData(response.data)
    } catch (err) {
      console.error('Error obteniendo seguimiento:', err)
      if (err.response?.status === 404) {
        setError('Pedido no encontrado. Verifica el número ingresado.')
      } else {
        setError('Error obteniendo información de seguimiento. Intenta nuevamente.')
      }
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status) => {
    const icons = {
      pending: '⏳',
      confirmed: '✅',
      processing: '📦',
      shipped: '🚚',
      delivered: '🎉',
      cancelled: '❌'
    }
    return icons[status] || '📋'
  }

  const getStatusColor = (status) => {
    const colors = {
      pending: '#f39c12',
      confirmed: '#3498db',
      processing: '#9b59b6',
      shipped: '#e67e22',
      delivered: '#27ae60',
      cancelled: '#e74c3c'
    }
    return colors[status] || '#95a5a6'
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'No disponible'
    
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return 'Fecha inválida'
    }
  }

  const getProgressPercentage = (status) => {
    const progressMap = {
      pending: 10,
      confirmed: 25,
      processing: 50,
      shipped: 75,
      delivered: 100,
      cancelled: 0
    }
    return progressMap[status] || 0
  }

  return (
    <div className="order-tracking">
      <div className="tracking-header">
        <h2>🔍 Seguimiento de Pedidos</h2>
        <p>Rastrea tu pedido en tiempo real</p>
      </div>

      <div className="tracking-search">
        <div className="search-container">
          <div className="search-type-selector">
            <button
              className={`type-btn ${searchType === 'order' ? 'active' : ''}`}
              onClick={() => setSearchType('order')}
            >
              📋 Número de Pedido
            </button>
            <button
              className={`type-btn ${searchType === 'tracking' ? 'active' : ''}`}
              onClick={() => setSearchType('tracking')}
            >
              📦 Número de Seguimiento
            </button>
          </div>

          <div className="search-input-container">
            {searchType === 'order' ? (
              <input
                type="text"
                placeholder="Ej: ORD-001234"
                value={orderNumber}
                onChange={(e) => setOrderNumber(e.target.value)}
                className="search-input"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            ) : (
              <input
                type="text"
                placeholder="Ej: TRK123456789"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                className="search-input"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            )}
            
            <button
              onClick={handleSearch}
              disabled={loading}
              className="search-btn"
            >
              {loading ? '🔄' : '🔍'}
              {loading ? 'Buscando...' : 'Buscar'}
            </button>
          </div>
        </div>

        {error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            {error}
          </div>
        )}
      </div>

      {trackingData && (
        <div className="tracking-results">
          <div className="order-summary">
            <div className="summary-header">
              <h3>Información del Pedido</h3>
              <div className="order-status">
                <span 
                  className="status-badge"
                  style={{ backgroundColor: getStatusColor(trackingData.current_status) }}
                >
                  {getStatusIcon(trackingData.current_status)}
                  {trackingData.current_status.replace('_', ' ').toUpperCase()}
                </span>
              </div>
            </div>

            <div className="summary-details">
              <div className="detail-item">
                <span className="detail-label">Número de Pedido:</span>
                <span className="detail-value">{trackingData.order_number}</span>
              </div>
              
              {trackingData.tracking_number && (
                <div className="detail-item">
                  <span className="detail-label">Número de Seguimiento:</span>
                  <span className="detail-value">{trackingData.tracking_number}</span>
                </div>
              )}
              
              {trackingData.estimated_delivery && (
                <div className="detail-item">
                  <span className="detail-label">Entrega Estimada:</span>
                  <span className="detail-value">
                    {formatDate(trackingData.estimated_delivery)}
                  </span>
                </div>
              )}
            </div>

            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ 
                  width: `${getProgressPercentage(trackingData.current_status)}%`,
                  backgroundColor: getStatusColor(trackingData.current_status)
                }}
              />
            </div>
          </div>

          <div className="tracking-timeline">
            <h3>Historial de Seguimiento</h3>
            
            {trackingData.tracking_events && trackingData.tracking_events.length > 0 ? (
              <div className="timeline">
                {trackingData.tracking_events
                  .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                  .map((event, index) => (
                    <div key={index} className="timeline-item">
                      <div className="timeline-marker">
                        <span className="timeline-icon">
                          {getStatusIcon(event.status)}
                        </span>
                      </div>
                      <div className="timeline-content">
                        <div className="timeline-header">
                          <h4>{event.description}</h4>
                          <span className="timeline-date">
                            {formatDate(event.timestamp)}
                          </span>
                        </div>
                        <div className="timeline-details">
                          {event.location && (
                            <p className="timeline-location">
                              📍 {event.location}
                            </p>
                          )}
                          {event.carrier && (
                            <p className="timeline-carrier">
                              🚛 {event.carrier}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="no-events">
                <p>No hay eventos de seguimiento disponibles</p>
              </div>
            )}
          </div>

          <div className="tracking-actions">
            <button
              onClick={() => {
                setTrackingData(null)
                setTrackingNumber('')
                setOrderNumber('')
                setError('')
              }}
              className="new-search-btn"
            >
              🔍 Nueva Búsqueda
            </button>
            
            <button
              onClick={() => window.print()}
              className="print-btn"
            >
              🖨️ Imprimir
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default OrderTracking
