"""
Servicio de gestión de pedidos y envíos.

Ruta: app/services/order_service.py
Responsabilidad: Lógica de negocio para pedidos, envíos y seguimiento
"""

import uuid
import random
from datetime import datetime, timedelta
from typing import List, Optional, Dict
import structlog

from app.schemas.orders import (
    CreateOrderRequest, Order, OrderStatus, PaymentStatus, 
    ShippingInfo, TrackingEvent, TrackingResponse
)

logger = structlog.get_logger()


class OrderService:
    """Servicio para gestión de pedidos."""
    
    def __init__(self):
        """Inicializa el servicio de pedidos."""
        # Simulación de base de datos en memoria
        self.orders: Dict[str, Order] = {}
        self.order_counter = 1000
        
        # Transportistas disponibles
        self.carriers = [
            "FedEx", "UPS", "DHL", "USPS", "Amazon Logistics"
        ]
        
        # Ubicaciones para simulación de seguimiento
        self.locations = [
            "Centro de Distribución - Ciudad Origen",
            "En Tránsito - Hub Regional",
            "Centro de Clasificación",
            "En Tránsito - Ciudad Destino",
            "Centro de Entrega Local",
            "En Reparto",
            "Entregado"
        ]
        
        logger.info("OrderService inicializado")
    
    def create_order(self, order_request: CreateOrderRequest) -> Order:
        """
        Crea un nuevo pedido.
        
        Args:
            order_request: Datos del pedido a crear
            
        Returns:
            Pedido creado
        """
        try:
            # Generar número de pedido único
            order_number = f"ORD-{self.order_counter:06d}"
            self.order_counter += 1
            
            # Crear pedido
            order = Order(
                id=self.order_counter,
                order_number=order_number,
                status=OrderStatus.PENDING,
                payment_status=PaymentStatus.PENDING,
                items=order_request.items,
                shipping_address=order_request.shipping_address,
                shipping_method=order_request.shipping_method,
                shipping_info=None,
                totals=order_request.totals,
                notes=order_request.notes,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # Guardar en "base de datos"
            self.orders[order_number] = order
            
            # Simular procesamiento de pago
            self._process_payment(order_number)
            
            logger.info(f"Pedido creado: {order_number}")
            return order
            
        except Exception as e:
            logger.error(f"Error creando pedido: {str(e)}")
            raise
    
    def get_order(self, order_number: str) -> Optional[Order]:
        """
        Obtiene un pedido por número.
        
        Args:
            order_number: Número de pedido
            
        Returns:
            Pedido encontrado o None
        """
        return self.orders.get(order_number)
    
    def get_orders(self, page: int = 1, per_page: int = 10) -> Dict:
        """
        Obtiene lista paginada de pedidos.
        
        Args:
            page: Página a obtener
            per_page: Items por página
            
        Returns:
            Diccionario con pedidos y metadatos de paginación
        """
        orders_list = list(self.orders.values())
        total_count = len(orders_list)
        
        # Ordenar por fecha de creación (más recientes primero)
        orders_list.sort(key=lambda x: x.created_at, reverse=True)
        
        # Paginación
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        paginated_orders = orders_list[start_idx:end_idx]
        
        return {
            "orders": paginated_orders,
            "total_count": total_count,
            "page": page,
            "per_page": per_page,
            "total_pages": (total_count + per_page - 1) // per_page
        }
    
    def update_order_status(self, order_number: str, status: OrderStatus, 
                           tracking_number: Optional[str] = None,
                           carrier: Optional[str] = None) -> Optional[Order]:
        """
        Actualiza el estado de un pedido.
        
        Args:
            order_number: Número de pedido
            status: Nuevo estado
            tracking_number: Número de seguimiento (opcional)
            carrier: Transportista (opcional)
            
        Returns:
            Pedido actualizado o None
        """
        order = self.orders.get(order_number)
        if not order:
            return None
        
        # Actualizar estado
        order.status = status
        order.updated_at = datetime.now()
        
        # Si se proporciona información de envío
        if tracking_number or carrier:
            if not order.shipping_info:
                order.shipping_info = ShippingInfo(
                    tracking_number=tracking_number,
                    carrier=carrier or random.choice(self.carriers),
                    estimated_delivery=self._calculate_estimated_delivery(order.shipping_method),
                    tracking_events=[]
                )
            else:
                if tracking_number:
                    order.shipping_info.tracking_number = tracking_number
                if carrier:
                    order.shipping_info.carrier = carrier
        
        # Agregar evento de seguimiento
        if order.shipping_info:
            event = TrackingEvent(
                timestamp=datetime.now(),
                status=status.value,
                location=self._get_location_for_status(status),
                description=self._get_description_for_status(status),
                carrier=order.shipping_info.carrier
            )
            order.shipping_info.tracking_events.append(event)
        
        logger.info(f"Estado de pedido actualizado: {order_number} -> {status.value}")
        return order
    
    def track_order(self, order_number: str) -> Optional[TrackingResponse]:
        """
        Obtiene información de seguimiento de un pedido.
        
        Args:
            order_number: Número de pedido
            
        Returns:
            Información de seguimiento o None
        """
        order = self.orders.get(order_number)
        if not order:
            return None
        
        # Simular actualización de seguimiento si el pedido está enviado
        if order.status == OrderStatus.SHIPPED and order.shipping_info:
            self._simulate_tracking_updates(order)
        
        return TrackingResponse(
            success=True,
            order_number=order_number,
            current_status=order.status,
            tracking_number=order.shipping_info.tracking_number if order.shipping_info else None,
            estimated_delivery=order.shipping_info.estimated_delivery if order.shipping_info else None,
            tracking_events=order.shipping_info.tracking_events if order.shipping_info else []
        )
    
    def _process_payment(self, order_number: str):
        """Simula el procesamiento de pago."""
        # Simular delay de procesamiento
        import time
        time.sleep(0.1)
        
        order = self.orders[order_number]
        
        # Simular éxito/fallo de pago (95% éxito)
        if random.random() < 0.95:
            order.payment_status = PaymentStatus.COMPLETED
            order.status = OrderStatus.CONFIRMED
            
            # Crear información de envío
            order.shipping_info = ShippingInfo(
                tracking_number=self._generate_tracking_number(),
                carrier=random.choice(self.carriers),
                estimated_delivery=self._calculate_estimated_delivery(order.shipping_method),
                tracking_events=[
                    TrackingEvent(
                        timestamp=datetime.now(),
                        status="confirmed",
                        location="Centro de Procesamiento",
                        description="Pedido confirmado y en preparación",
                        carrier=random.choice(self.carriers)
                    )
                ]
            )
        else:
            order.payment_status = PaymentStatus.FAILED
            order.status = OrderStatus.CANCELLED
    
    def _generate_tracking_number(self) -> str:
        """Genera un número de seguimiento simulado."""
        return f"TRK{random.randint(100000000, 999999999)}"
    
    def _calculate_estimated_delivery(self, shipping_method) -> datetime:
        """Calcula fecha estimada de entrega."""
        days_map = {
            "standard": random.randint(5, 7),
            "express": random.randint(2, 3),
            "overnight": 1
        }
        
        days = days_map.get(shipping_method.value, 5)
        return datetime.now() + timedelta(days=days)
    
    def _get_location_for_status(self, status: OrderStatus) -> str:
        """Obtiene ubicación para un estado."""
        location_map = {
            OrderStatus.PENDING: "Centro de Procesamiento",
            OrderStatus.CONFIRMED: "Centro de Procesamiento",
            OrderStatus.PROCESSING: "Centro de Distribución",
            OrderStatus.SHIPPED: "En Tránsito",
            OrderStatus.DELIVERED: "Dirección de Entrega",
            OrderStatus.CANCELLED: "Centro de Procesamiento"
        }
        return location_map.get(status, "Ubicación Desconocida")
    
    def _get_description_for_status(self, status: OrderStatus) -> str:
        """Obtiene descripción para un estado."""
        description_map = {
            OrderStatus.PENDING: "Pedido recibido y pendiente de confirmación",
            OrderStatus.CONFIRMED: "Pedido confirmado y en preparación",
            OrderStatus.PROCESSING: "Pedido en proceso de empaque",
            OrderStatus.SHIPPED: "Pedido enviado y en tránsito",
            OrderStatus.DELIVERED: "Pedido entregado exitosamente",
            OrderStatus.CANCELLED: "Pedido cancelado"
        }
        return description_map.get(status, "Estado actualizado")
    
    def _simulate_tracking_updates(self, order: Order):
        """Simula actualizaciones de seguimiento automáticas."""
        if not order.shipping_info:
            return
        
        # Solo agregar eventos si han pasado al menos 30 minutos desde el último
        last_event = order.shipping_info.tracking_events[-1] if order.shipping_info.tracking_events else None
        if last_event and (datetime.now() - last_event.timestamp).seconds < 1800:
            return
        
        # Simular progreso del envío
        current_events = len(order.shipping_info.tracking_events)
        if current_events < len(self.locations) and random.random() < 0.3:
            new_event = TrackingEvent(
                timestamp=datetime.now(),
                status="in_transit",
                location=self.locations[min(current_events, len(self.locations) - 1)],
                description=f"Paquete procesado en {self.locations[min(current_events, len(self.locations) - 1)]}",
                carrier=order.shipping_info.carrier
            )
            order.shipping_info.tracking_events.append(new_event)
            
            # Si llegó al final, marcar como entregado
            if current_events >= len(self.locations) - 1:
                order.status = OrderStatus.DELIVERED
                order.shipping_info.actual_delivery = datetime.now()
