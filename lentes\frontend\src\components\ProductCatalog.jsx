import React, { useState, useEffect } from 'react'
import './ProductCatalog.css'

const ProductCatalog = ({ onAddToCart, onTryVirtual }) => {
  const [products, setProducts] = useState([])
  const [filteredProducts, setFilteredProducts] = useState([])
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedBrand, setSelectedBrand] = useState('all')
  const [priceRange, setPriceRange] = useState([0, 500])
  const [sortBy, setSortBy] = useState('name')

  // Datos de productos simulados
  useEffect(() => {
    const mockProducts = [
      {
        id: 1,
        name: 'Ray-Ban Aviator Classic',
        brand: 'Ray-Ban',
        category: 'sunglasses',
        price: 159.99,
        originalPrice: 199.99,
        image: '/api/placeholder/300/200',
        rating: 4.8,
        reviews: 1250,
        colors: ['black', 'gold', 'silver'],
        description: 'Los icónicos lentes aviador con cristales de alta calidad',
        features: ['Protección UV 100%', 'Cristales polarizados', 'Marco de metal'],
        inStock: true,
        isNew: false,
        isOnSale: true
      },
      {
        id: 2,
        name: 'Oakley Holbrook',
        brand: 'Oakley',
        category: 'sunglasses',
        price: 129.99,
        originalPrice: 149.99,
        image: '/api/placeholder/300/200',
        rating: 4.6,
        reviews: 890,
        colors: ['black', 'blue', 'gray'],
        description: 'Diseño deportivo con tecnología avanzada',
        features: ['Prizm Technology', 'Marco O Matter', 'Resistente a impactos'],
        inStock: true,
        isNew: true,
        isOnSale: true
      },
      {
        id: 3,
        name: 'Warby Parker Clark',
        brand: 'Warby Parker',
        category: 'glasses',
        price: 95.00,
        originalPrice: 95.00,
        image: '/api/placeholder/300/200',
        rating: 4.7,
        reviews: 650,
        colors: ['brown', 'black', 'blue'],
        description: 'Lentes clásicos para uso diario con estilo atemporal',
        features: ['Acetato premium', 'Lentes antirreflejantes', 'Ajuste cómodo'],
        inStock: true,
        isNew: false,
        isOnSale: false
      },
      {
        id: 4,
        name: 'Tom Ford FT5401',
        brand: 'Tom Ford',
        category: 'glasses',
        price: 299.99,
        originalPrice: 350.00,
        image: '/api/placeholder/300/200',
        rating: 4.9,
        reviews: 420,
        colors: ['black', 'brown', 'gray'],
        description: 'Elegancia y sofisticación en cada detalle',
        features: ['Diseño italiano', 'Acetato de lujo', 'Acabado premium'],
        inStock: true,
        isNew: true,
        isOnSale: true
      },
      {
        id: 5,
        name: 'Persol PO3019S',
        brand: 'Persol',
        category: 'sunglasses',
        price: 189.99,
        originalPrice: 220.00,
        image: '/api/placeholder/300/200',
        rating: 4.8,
        reviews: 310,
        colors: ['brown', 'black', 'green'],
        description: 'Artesanía italiana con cristales de calidad superior',
        features: ['Cristal mineral', 'Patillas flexibles', 'Protección UV'],
        inStock: false,
        isNew: false,
        isOnSale: true
      },
      {
        id: 6,
        name: 'Maui Jim Peahi',
        brand: 'Maui Jim',
        category: 'sunglasses',
        price: 249.99,
        originalPrice: 279.99,
        image: '/api/placeholder/300/200',
        rating: 4.9,
        reviews: 180,
        colors: ['blue', 'gray', 'brown'],
        description: 'Tecnología PolarizedPlus2 para colores vibrantes',
        features: ['PolarizedPlus2', 'Marco SuperThin', 'Lentes de cristal'],
        inStock: true,
        isNew: true,
        isOnSale: true
      }
    ]
    
    setProducts(mockProducts)
    setFilteredProducts(mockProducts)
  }, [])

  // Filtrar productos
  useEffect(() => {
    let filtered = products.filter(product => {
      const categoryMatch = selectedCategory === 'all' || product.category === selectedCategory
      const brandMatch = selectedBrand === 'all' || product.brand === selectedBrand
      const priceMatch = product.price >= priceRange[0] && product.price <= priceRange[1]
      
      return categoryMatch && brandMatch && priceMatch
    })

    // Ordenar productos
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price
        case 'price-high':
          return b.price - a.price
        case 'rating':
          return b.rating - a.rating
        case 'name':
        default:
          return a.name.localeCompare(b.name)
      }
    })

    setFilteredProducts(filtered)
  }, [products, selectedCategory, selectedBrand, priceRange, sortBy])

  const categories = [
    { value: 'all', label: 'Todos', icon: '👓' },
    { value: 'glasses', label: 'Lentes Ópticos', icon: '🤓' },
    { value: 'sunglasses', label: 'Lentes de Sol', icon: '🕶️' },
    { value: 'reading', label: 'Lectura', icon: '📖' },
    { value: 'safety', label: 'Seguridad', icon: '🥽' }
  ]

  const brands = ['all', 'Ray-Ban', 'Oakley', 'Warby Parker', 'Tom Ford', 'Persol', 'Maui Jim']

  const handleAddToCart = (product, selectedColor = product.colors[0]) => {
    const cartItem = {
      ...product,
      selectedColor,
      quantity: 1,
      cartId: `${product.id}-${selectedColor}`
    }
    onAddToCart(cartItem)
  }

  return (
    <div className="product-catalog">
      <div className="catalog-header">
        <h2>Catálogo de Lentes</h2>
        <p>Descubre nuestra colección premium de lentes</p>
      </div>

      <div className="catalog-container">
        {/* Filtros laterales */}
        <aside className="filters-sidebar">
          <div className="filter-section">
            <h3>Categorías</h3>
            <div className="category-filters">
              {categories.map(category => (
                <button
                  key={category.value}
                  className={`category-btn ${selectedCategory === category.value ? 'active' : ''}`}
                  onClick={() => setSelectedCategory(category.value)}
                >
                  <span className="category-icon">{category.icon}</span>
                  <span className="category-label">{category.label}</span>
                </button>
              ))}
            </div>
          </div>

          <div className="filter-section">
            <h3>Marca</h3>
            <select
              value={selectedBrand}
              onChange={(e) => setSelectedBrand(e.target.value)}
              className="brand-select"
            >
              {brands.map(brand => (
                <option key={brand} value={brand}>
                  {brand === 'all' ? 'Todas las marcas' : brand}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-section">
            <h3>Rango de Precio</h3>
            <div className="price-range">
              <input
                type="range"
                min="0"
                max="500"
                value={priceRange[1]}
                onChange={(e) => setPriceRange([0, parseInt(e.target.value)])}
                className="price-slider"
              />
              <div className="price-display">
                $0 - ${priceRange[1]}
              </div>
            </div>
          </div>
        </aside>

        {/* Productos */}
        <main className="products-main">
          <div className="products-header">
            <div className="results-info">
              <span>{filteredProducts.length} productos encontrados</span>
            </div>
            <div className="sort-controls">
              <label>Ordenar por:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="sort-select"
              >
                <option value="name">Nombre</option>
                <option value="price-low">Precio: Menor a Mayor</option>
                <option value="price-high">Precio: Mayor a Menor</option>
                <option value="rating">Mejor Valorados</option>
              </select>
            </div>
          </div>

          <div className="products-grid">
            {filteredProducts.map(product => (
              <div key={product.id} className="product-card">
                {product.isNew && <span className="badge new-badge">Nuevo</span>}
                {product.isOnSale && <span className="badge sale-badge">Oferta</span>}
                {!product.inStock && <span className="badge stock-badge">Agotado</span>}

                <div className="product-image">
                  <img src={product.image} alt={product.name} />
                  <div className="product-overlay">
                    <button
                      className="try-virtual-btn"
                      onClick={() => onTryVirtual && onTryVirtual(product)}
                    >
                      <span>👁️</span>
                      Probar Virtual
                    </button>
                  </div>
                </div>

                <div className="product-info">
                  <div className="product-brand">{product.brand}</div>
                  <h3 className="product-name">{product.name}</h3>
                  <div className="product-rating">
                    <span className="stars">{'★'.repeat(Math.floor(product.rating))}</span>
                    <span className="rating-text">({product.reviews})</span>
                  </div>
                  <div className="product-price">
                    <span className="current-price">${product.price}</span>
                    {product.originalPrice > product.price && (
                      <span className="original-price">${product.originalPrice}</span>
                    )}
                  </div>
                  <div className="product-colors">
                    {product.colors.map(color => (
                      <span
                        key={color}
                        className="color-dot"
                        style={{ backgroundColor: color }}
                        title={color}
                      />
                    ))}
                  </div>
                  <button
                    className={`add-to-cart-btn ${!product.inStock ? 'disabled' : ''}`}
                    onClick={() => handleAddToCart(product)}
                    disabled={!product.inStock}
                  >
                    {product.inStock ? '🛒 Agregar al Carrito' : 'Agotado'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </main>
      </div>
    </div>
  )
}

export default ProductCatalog
