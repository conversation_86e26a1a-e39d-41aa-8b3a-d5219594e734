import React, { useState } from 'react'
import './Header.css'

const Header = ({ currentView, onViewChange, cartItems, onCartToggle }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const cartItemCount = cartItems.reduce((total, item) => total + item.quantity, 0)

  const navigationItems = [
    { id: 'camera', label: 'Prueba Virtual', icon: '📹' },
    { id: 'catalog', label: 'Catálogo', icon: '🕶️' },
    { id: 'tracking', label: 'Seguimiento', icon: '📦' },
    { id: 'about', label: 'Nosotros', icon: 'ℹ️' },
    { id: 'contact', label: 'Contacto', icon: '📞' }
  ]

  return (
    <header className="header">
      <div className="header-container">
        {/* Logo y marca */}
        <div className="brand">
          <div className="logo">
            <span className="logo-icon">👁️</span>
            <div className="logo-text">
              <h1>OpticaVirtual</h1>
              <span className="tagline">Tu visión, nuestro compromiso</span>
            </div>
          </div>
        </div>

        {/* Navegación principal */}
        <nav className={`main-nav ${isMenuOpen ? 'open' : ''}`}>
          <ul className="nav-list">
            {navigationItems.map(item => (
              <li key={item.id} className="nav-item">
                <button
                  className={`nav-link ${currentView === item.id ? 'active' : ''}`}
                  onClick={() => {
                    onViewChange(item.id)
                    setIsMenuOpen(false)
                  }}
                >
                  <span className="nav-icon">{item.icon}</span>
                  <span className="nav-label">{item.label}</span>
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Acciones del header */}
        <div className="header-actions">
          {/* Búsqueda */}
          <div className="search-container">
            <input
              type="text"
              placeholder="Buscar lentes..."
              className="search-input"
            />
            <button className="search-btn">
              <span>🔍</span>
            </button>
          </div>

          {/* Carrito */}
          <button
            className="cart-btn"
            onClick={onCartToggle}
          >
            <span className="cart-icon">🛒</span>
            {cartItemCount > 0 && (
              <span className="cart-badge">{cartItemCount}</span>
            )}
            <span className="cart-label">Carrito</span>
          </button>

          {/* Menú móvil */}
          <button
            className="mobile-menu-btn"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <span className={`hamburger ${isMenuOpen ? 'open' : ''}`}>
              <span></span>
              <span></span>
              <span></span>
            </span>
          </button>
        </div>
      </div>

      {/* Barra de promociones */}
      <div className="promo-bar">
        <div className="promo-content">
          <span className="promo-icon">🎉</span>
          <span className="promo-text">
            ¡Envío GRATIS en compras mayores a $50! | Garantía de 30 días
          </span>
          <span className="promo-icon">✨</span>
        </div>
      </div>
    </header>
  )
}

export default Header
