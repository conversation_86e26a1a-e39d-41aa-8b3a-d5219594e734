# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/tensor/vector_to_tensor_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n>mediapipe/calculators/tensor/vector_to_tensor_calculator.proto\x12\tmediapipe\"F\n\x1fVectorToTensorCalculatorOptions\x12#\n\x1boutput_dynamic_tensor_shape\x18\x01 \x01(\x08\x42\x42\n\x1a\x63om.google.mediapipe.protoB$VectorToTensorCalculatorOptionsProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.tensor.vector_to_tensor_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\032com.google.mediapipe.protoB$VectorToTensorCalculatorOptionsProto'
  _globals['_VECTORTOTENSORCALCULATOROPTIONS']._serialized_start=77
  _globals['_VECTORTOTENSORCALCULATOROPTIONS']._serialized_end=147
# @@protoc_insertion_point(module_scope)
