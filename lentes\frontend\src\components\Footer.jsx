import React from 'react'
import './Footer.css'

const Footer = ({ onNavigate }) => {
  return (
    <footer className="footer">
      <div className="footer-container">
        <div className="footer-content">
          {/* Información de la empresa */}
          <div className="footer-section">
            <div className="footer-logo">
              <span className="logo-icon">👁️</span>
              <h3>OpticaVirtual</h3>
            </div>
            <p className="footer-description">
              Revolucionando la experiencia de compra de lentes con tecnología de realidad virtual 
              y inteligencia artificial. Tu visión perfecta está a un clic de distancia.
            </p>
            <div className="social-links">
              <a href="#" className="social-link" title="Facebook">📘</a>
              <a href="#" className="social-link" title="Instagram">📷</a>
              <a href="#" className="social-link" title="Twitter">🐦</a>
              <a href="#" className="social-link" title="LinkedIn">💼</a>
            </div>
          </div>

          {/* Enlaces rápidos */}
          <div className="footer-section">
            <h4>Enlaces Rápidos</h4>
            <ul className="footer-links">
              <li><a href="#catalog">Catálogo</a></li>
              <li><a href="#virtual-try">Prueba Virtual</a></li>
              <li><a href="#brands">Marcas</a></li>
              <li><a href="#offers">Ofertas</a></li>
              <li><a href="#new-arrivals">Nuevos Productos</a></li>
            </ul>
          </div>

          {/* Atención al cliente */}
          <div className="footer-section">
            <h4>Atención al Cliente</h4>
            <ul className="footer-links">
              <li><a href="#contact">Contacto</a></li>
              <li><a href="#shipping">Envíos</a></li>
              <li><a href="#returns">Devoluciones</a></li>
              <li><a href="#warranty">Garantía</a></li>
              <li><a href="#faq">Preguntas Frecuentes</a></li>
            </ul>
          </div>

          {/* Información de contacto */}
          <div className="footer-section">
            <h4>Contacto</h4>
            <div className="contact-info">
              <div className="contact-item">
                <span className="contact-icon">📍</span>
                <span>123 Calle Principal, Ciudad, País</span>
              </div>
              <div className="contact-item">
                <span className="contact-icon">📞</span>
                <span>+1 (555) 123-4567</span>
              </div>
              <div className="contact-item">
                <span className="contact-icon">✉️</span>
                <span><EMAIL></span>
              </div>
              <div className="contact-item">
                <span className="contact-icon">🕒</span>
                <span>Lun-Vie: 9:00-18:00</span>
              </div>
            </div>
          </div>

          {/* Newsletter */}
          <div className="footer-section">
            <h4>Newsletter</h4>
            <p className="newsletter-text">
              Suscríbete para recibir ofertas exclusivas y novedades
            </p>
            <div className="newsletter-form">
              <input
                type="email"
                placeholder="Tu email"
                className="newsletter-input"
              />
              <button className="newsletter-btn">
                Suscribirse
              </button>
            </div>
            <div className="newsletter-benefits">
              <div className="benefit-item">
                <span className="benefit-icon">🎁</span>
                <span>Ofertas exclusivas</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">📦</span>
                <span>Envío gratuito</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">🔔</span>
                <span>Nuevos productos</span>
              </div>
            </div>
          </div>
        </div>

        {/* Certificaciones y garantías */}
        <div className="footer-certifications">
          <div className="cert-item">
            <span className="cert-icon">🔒</span>
            <span>Compra Segura</span>
          </div>
          <div className="cert-item">
            <span className="cert-icon">🚚</span>
            <span>Envío Rápido</span>
          </div>
          <div className="cert-item">
            <span className="cert-icon">↩️</span>
            <span>30 Días Garantía</span>
          </div>
          <div className="cert-item">
            <span className="cert-icon">💳</span>
            <span>Pago Seguro</span>
          </div>
          <div className="cert-item">
            <span className="cert-icon">🏆</span>
            <span>Calidad Premium</span>
          </div>
        </div>

        {/* Línea divisoria */}
        <div className="footer-divider"></div>

        {/* Copyright y enlaces legales */}
        <div className="footer-bottom">
          <div className="copyright">
            <p>&copy; 2024 OpticaVirtual. Todos los derechos reservados.</p>
            <p className="tech-credit">Powered by AI & Computer Vision Technology</p>
          </div>
          <div className="legal-links">
            <button onClick={() => onNavigate && onNavigate('privacy')}>Política de Privacidad</button>
            <button onClick={() => onNavigate && onNavigate('terms')}>Términos de Servicio</button>
            <button onClick={() => onNavigate && onNavigate('cookies')}>Política de Cookies</button>
            <button onClick={() => onNavigate && onNavigate('accessibility')}>Accesibilidad</button>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
