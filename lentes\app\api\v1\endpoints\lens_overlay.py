"""
Endpoints para la funcionalidad de superposición de lentes.

Ruta: app/api/v1/endpoints/lens_overlay.py
Responsabilidad: Manejo de endpoints para aplicar lentes virtuales
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import StreamingResponse
from typing import Optional, List, Dict
import io
import cv2
import numpy as np
from PIL import Image
import mediapipe as mp
from pathlib import Path
import os

from app.core.config import settings
from app.services.face_detection import FaceDetectionService
from app.services.lens_overlay import LensOverlayService
from app.schemas.lens_overlay import LensOverlayResponse, LensType

router = APIRouter()

# Inicializar servicios
face_detection_service = FaceDetectionService()
lens_overlay_service = LensOverlayService()


@router.post("/apply", response_model=LensOverlayResponse)
async def apply_lens_overlay(
    image: UploadFile = File(...),
    lens_type: LensType = LensType.GLASSES,
    lens_color: Optional[str] = "blue"
):
    """
    Aplica una superposición de lentes a una imagen.
    
    Args:
        image: Imagen a procesar
        lens_type: Tipo de lente a aplicar
        lens_color: Color del lente (opcional)
    
    Returns:
        Respuesta con la imagen procesada
    """
    try:
        # Validar tipo de archivo
        if image.content_type not in settings.ALLOWED_IMAGE_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"Tipo de archivo no soportado. Tipos permitidos: {settings.ALLOWED_IMAGE_TYPES}"
            )
        
        # Validar tamaño de archivo
        contents = await image.read()
        if len(contents) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"Archivo demasiado grande. Tamaño máximo: {settings.MAX_FILE_SIZE} bytes"
            )
        
        # Convertir a imagen OpenCV
        nparr = np.frombuffer(contents, np.uint8)
        cv_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if cv_image is None:
            raise HTTPException(status_code=400, detail="No se pudo procesar la imagen")
        
        # Detectar rostros
        faces = face_detection_service.detect_faces(cv_image)
        
        if not faces:
            raise HTTPException(status_code=404, detail="No se detectaron rostros en la imagen")
        
        # Aplicar superposición de lentes
        result_image = lens_overlay_service.apply_lens(
            cv_image, faces, lens_type, lens_color
        )
        
        # Convertir resultado a bytes
        _, buffer = cv2.imencode('.png', result_image)
        image_bytes = buffer.tobytes()
        
        return LensOverlayResponse(
            success=True,
            message="Lentes aplicados exitosamente",
            faces_detected=len(faces),
            lens_type=lens_type,
            lens_color=lens_color
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error interno del servidor: {str(e)}")


@router.post("/apply-stream")
async def apply_lens_overlay_stream(
    image: UploadFile = File(...),
    lens_type: LensType = LensType.GLASSES,
    lens_color: Optional[str] = "blue"
):
    """
    Aplica lentes y retorna la imagen como stream.
    
    Args:
        image: Imagen a procesar
        lens_type: Tipo de lente a aplicar
        lens_color: Color del lente
    
    Returns:
        Stream de la imagen procesada
    """
    try:
        import structlog
        logger = structlog.get_logger()
        logger.info(f"Procesando imagen: {image.filename}, tipo: {image.content_type}")

        # Validaciones similares al endpoint anterior
        if image.content_type not in settings.ALLOWED_IMAGE_TYPES:
            logger.error(f"Tipo de archivo no soportado: {image.content_type}")
            raise HTTPException(
                status_code=400,
                detail=f"Tipo de archivo no soportado. Tipos permitidos: {settings.ALLOWED_IMAGE_TYPES}"
            )
        
        contents = await image.read()
        if len(contents) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"Archivo demasiado grande. Tamaño máximo: {settings.MAX_FILE_SIZE} bytes"
            )
        
        # Procesar imagen
        nparr = np.frombuffer(contents, np.uint8)
        cv_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if cv_image is None:
            raise HTTPException(status_code=400, detail="No se pudo procesar la imagen")
        
        # Detectar rostros y aplicar lentes
        logger.info("Iniciando detección de rostros")
        faces = face_detection_service.detect_faces(cv_image)
        logger.info(f"Rostros detectados: {len(faces)}")

        if not faces:
            logger.warning("No se detectaron rostros en la imagen")
            raise HTTPException(status_code=404, detail="No se detectaron rostros en la imagen")
        
        logger.info(f"Aplicando lentes: tipo={lens_type}, color={lens_color}")
        result_image = lens_overlay_service.apply_lens(
            cv_image, faces, lens_type, lens_color
        )
        logger.info("Lentes aplicados exitosamente")

        # Convertir a stream
        _, buffer = cv2.imencode('.png', result_image)
        image_stream = io.BytesIO(buffer.tobytes())
        logger.info(f"Imagen convertida a stream: {len(buffer)} bytes")

        return StreamingResponse(
            image_stream,
            media_type="image/png",
            headers={
                "Content-Disposition": "inline; filename=lens_overlay.png",
                "X-Faces-Detected": str(len(faces))
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error interno del servidor: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error interno del servidor: {str(e)}")


@router.get("/lens-types")
async def get_available_lens_types():
    """
    Obtiene los tipos de lentes disponibles.

    Returns:
        Lista de tipos de lentes disponibles
    """
    return {
        "lens_types": [lens_type.value for lens_type in LensType],
        "default_colors": ["blue", "red", "green", "black", "brown", "gray", "gold", "silver"]
    }


@router.get("/lens-gallery")
async def get_lens_gallery():
    """
    Obtiene la galería completa de lentes disponibles con sus imágenes.

    Returns:
        Diccionario con todos los lentes disponibles organizados por tipo y color
    """
    try:
        lens_path = Path("static/lenses")
        gallery = {}

        # Verificar que el directorio existe
        if not lens_path.exists():
            return {"error": "Directorio de lentes no encontrado", "gallery": {}}

        # Obtener todos los archivos PNG en el directorio
        lens_files = list(lens_path.glob("*.png"))

        for lens_file in lens_files:
            # Parsear nombre del archivo: tipo_color.png
            name_parts = lens_file.stem.split("_")
            if len(name_parts) >= 2:
                lens_type = name_parts[0]
                lens_color = "_".join(name_parts[1:])  # En caso de colores compuestos

                # Inicializar tipo si no existe
                if lens_type not in gallery:
                    gallery[lens_type] = {}

                # Agregar información del lente
                gallery[lens_type][lens_color] = {
                    "filename": lens_file.name,
                    "path": f"/static/lenses/{lens_file.name}",
                    "type": lens_type,
                    "color": lens_color,
                    "display_name": f"{lens_type.replace('_', ' ').title()} - {lens_color.replace('_', ' ').title()}"
                }

        return {
            "success": True,
            "total_lenses": sum(len(colors) for colors in gallery.values()),
            "gallery": gallery
        }

    except Exception as e:
        return {
            "success": False,
            "error": f"Error obteniendo galería: {str(e)}",
            "gallery": {}
        }


@router.get("/test")
async def test_endpoint():
    """Endpoint de prueba simple."""
    return {"message": "Endpoint funcionando correctamente", "status": "ok"}


@router.post("/test-upload")
async def test_upload(image: UploadFile = File(...)):
    """Endpoint de prueba para subida de archivos."""
    try:
        import structlog
        logger = structlog.get_logger()

        logger.info(f"Archivo recibido: {image.filename}, tipo: {image.content_type}, tamaño: {image.size}")

        contents = await image.read()
        logger.info(f"Contenido leído: {len(contents)} bytes")

        return {
            "message": "Archivo recibido correctamente",
            "filename": image.filename,
            "content_type": image.content_type,
            "size": len(contents)
        }
    except Exception as e:
        logger.error(f"Error en test-upload: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")
