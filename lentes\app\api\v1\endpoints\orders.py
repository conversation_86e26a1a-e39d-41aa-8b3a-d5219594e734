"""
Endpoints para gestión de pedidos y envíos.

Ruta: app/api/v1/endpoints/orders.py
Responsabilidad: Endpoints REST para pedidos, envíos y seguimiento
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Optional
import structlog

from app.schemas.orders import (
    CreateOrderRequest, OrderResponse, TrackingResponse, 
    OrderListResponse, UpdateOrderStatusRequest, OrderStatus
)
from app.services.order_service import OrderService

logger = structlog.get_logger()
router = APIRouter()

# Instancia del servicio de pedidos
order_service = OrderService()


@router.post("/create", response_model=OrderResponse)
async def create_order(order_request: CreateOrderRequest):
    """
    Crea un nuevo pedido.
    
    Args:
        order_request: Datos del pedido a crear
        
    Returns:
        Respuesta con el pedido creado
    """
    try:
        order = order_service.create_order(order_request)
        
        return OrderResponse(
            success=True,
            message="Pedido creado exitosamente",
            order=order,
            estimated_delivery=order.shipping_info.estimated_delivery.strftime("%Y-%m-%d") 
                if order.shipping_info and order.shipping_info.estimated_delivery else None
        )
        
    except Exception as e:
        logger.error(f"Error creando pedido: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error interno del servidor: {str(e)}"
        )


@router.get("/{order_number}", response_model=OrderResponse)
async def get_order(order_number: str):
    """
    Obtiene un pedido por número.
    
    Args:
        order_number: Número de pedido
        
    Returns:
        Datos del pedido
    """
    try:
        order = order_service.get_order(order_number)
        
        if not order:
            raise HTTPException(
                status_code=404,
                detail="Pedido no encontrado"
            )
        
        return OrderResponse(
            success=True,
            message="Pedido encontrado",
            order=order,
            estimated_delivery=order.shipping_info.estimated_delivery.strftime("%Y-%m-%d") 
                if order.shipping_info and order.shipping_info.estimated_delivery else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error obteniendo pedido {order_number}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error interno del servidor: {str(e)}"
        )


@router.get("/", response_model=OrderListResponse)
async def get_orders(
    page: int = Query(1, ge=1, description="Página a obtener"),
    per_page: int = Query(10, ge=1, le=100, description="Items por página")
):
    """
    Obtiene lista paginada de pedidos.
    
    Args:
        page: Página a obtener
        per_page: Items por página
        
    Returns:
        Lista paginada de pedidos
    """
    try:
        result = order_service.get_orders(page=page, per_page=per_page)
        
        return OrderListResponse(
            success=True,
            orders=result["orders"],
            total_count=result["total_count"],
            page=result["page"],
            per_page=result["per_page"]
        )
        
    except Exception as e:
        logger.error(f"Error obteniendo lista de pedidos: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error interno del servidor: {str(e)}"
        )


@router.put("/{order_number}/status", response_model=OrderResponse)
async def update_order_status(
    order_number: str,
    status_request: UpdateOrderStatusRequest
):
    """
    Actualiza el estado de un pedido.
    
    Args:
        order_number: Número de pedido
        status_request: Nuevo estado y datos adicionales
        
    Returns:
        Pedido actualizado
    """
    try:
        order = order_service.update_order_status(
            order_number=order_number,
            status=status_request.status,
            tracking_number=status_request.tracking_number,
            carrier=status_request.carrier
        )
        
        if not order:
            raise HTTPException(
                status_code=404,
                detail="Pedido no encontrado"
            )
        
        return OrderResponse(
            success=True,
            message=f"Estado del pedido actualizado a {status_request.status.value}",
            order=order
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error actualizando estado del pedido {order_number}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error interno del servidor: {str(e)}"
        )


@router.get("/{order_number}/tracking", response_model=TrackingResponse)
async def track_order(order_number: str):
    """
    Obtiene información de seguimiento de un pedido.
    
    Args:
        order_number: Número de pedido
        
    Returns:
        Información de seguimiento
    """
    try:
        tracking_info = order_service.track_order(order_number)
        
        if not tracking_info:
            raise HTTPException(
                status_code=404,
                detail="Pedido no encontrado"
            )
        
        return tracking_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error obteniendo seguimiento del pedido {order_number}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error interno del servidor: {str(e)}"
        )


@router.post("/{order_number}/simulate-progress")
async def simulate_order_progress(order_number: str):
    """
    Simula el progreso de un pedido (para demostración).
    
    Args:
        order_number: Número de pedido
        
    Returns:
        Mensaje de confirmación
    """
    try:
        order = order_service.get_order(order_number)
        
        if not order:
            raise HTTPException(
                status_code=404,
                detail="Pedido no encontrado"
            )
        
        # Simular progreso del pedido
        if order.status == OrderStatus.CONFIRMED:
            order_service.update_order_status(order_number, OrderStatus.PROCESSING)
        elif order.status == OrderStatus.PROCESSING:
            order_service.update_order_status(order_number, OrderStatus.SHIPPED)
        elif order.status == OrderStatus.SHIPPED:
            order_service.update_order_status(order_number, OrderStatus.DELIVERED)
        
        return {
            "success": True,
            "message": f"Progreso del pedido {order_number} simulado",
            "new_status": order.status.value
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error simulando progreso del pedido {order_number}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error interno del servidor: {str(e)}"
        )


@router.get("/tracking/{tracking_number}", response_model=TrackingResponse)
async def track_by_tracking_number(tracking_number: str):
    """
    Obtiene información de seguimiento por número de tracking.
    
    Args:
        tracking_number: Número de seguimiento
        
    Returns:
        Información de seguimiento
    """
    try:
        # Buscar pedido por número de tracking
        for order_number, order in order_service.orders.items():
            if (order.shipping_info and 
                order.shipping_info.tracking_number == tracking_number):
                
                tracking_info = order_service.track_order(order_number)
                return tracking_info
        
        raise HTTPException(
            status_code=404,
            detail="Número de seguimiento no encontrado"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error obteniendo seguimiento por tracking {tracking_number}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error interno del servidor: {str(e)}"
        )


@router.get("/status/options")
async def get_order_status_options():
    """
    Obtiene las opciones de estado de pedido disponibles.
    
    Returns:
        Lista de estados disponibles
    """
    return {
        "success": True,
        "statuses": [
            {
                "value": status.value,
                "label": status.value.replace("_", " ").title(),
                "description": _get_status_description(status)
            }
            for status in OrderStatus
        ]
    }


def _get_status_description(status: OrderStatus) -> str:
    """Obtiene descripción para un estado de pedido."""
    descriptions = {
        OrderStatus.PENDING: "Pedido recibido, pendiente de confirmación",
        OrderStatus.CONFIRMED: "Pedido confirmado, en preparación",
        OrderStatus.PROCESSING: "Pedido en proceso de empaque",
        OrderStatus.SHIPPED: "Pedido enviado, en tránsito",
        OrderStatus.DELIVERED: "Pedido entregado exitosamente",
        OrderStatus.CANCELLED: "Pedido cancelado"
    }
    return descriptions.get(status, "Estado desconocido")
