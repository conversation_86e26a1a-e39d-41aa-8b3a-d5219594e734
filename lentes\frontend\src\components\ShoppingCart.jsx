import React, { useState } from 'react'
import axios from 'axios'
import './ShoppingCart.css'

const ShoppingCart = ({ 
  isOpen, 
  onClose, 
  cartItems, 
  onUpdateQuantity, 
  onRemoveItem, 
  onCheckout 
}) => {
  const [isCheckingOut, setIsCheckingOut] = useState(false)
  const [shippingInfo, setShippingInfo] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    shippingMethod: 'standard'
  })

  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0)
  const shippingCost = subtotal > 50 ? 0 : 9.99
  const tax = subtotal * 0.08 // 8% tax
  const total = subtotal + shippingCost + tax

  const shippingMethods = [
    { id: 'standard', name: 'Env<PERSON>stándar', time: '5-7 días', cost: 9.99 },
    { id: 'express', name: 'Envío Express', time: '2-3 días', cost: 19.99 },
    { id: 'overnight', name: 'Envío Nocturno', time: '1 día', cost: 39.99 }
  ]

  const handleInputChange = (field, value) => {
    setShippingInfo(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleCheckout = async () => {
    setIsCheckingOut(true)

    try {
      // Preparar datos del pedido
      const orderRequest = {
        items: cartItems.map(item => ({
          product_id: item.id,
          product_name: item.name,
          brand: item.brand,
          selected_color: item.selectedColor,
          quantity: item.quantity,
          unit_price: item.price,
          total_price: item.price * item.quantity
        })),
        shipping_address: {
          name: shippingInfo.name,
          email: shippingInfo.email,
          phone: shippingInfo.phone,
          address: shippingInfo.address,
          city: shippingInfo.city,
          zip_code: shippingInfo.zipCode,
          country: 'US'
        },
        shipping_method: shippingInfo.shippingMethod,
        totals: {
          subtotal,
          shipping_cost: shippingCost,
          tax,
          discount: 0,
          total
        },
        notes: `Pedido realizado desde OpticaVirtual - ${new Date().toISOString()}`
      }

      // Enviar pedido al backend
      const response = await axios.post('/api/v1/orders/create', orderRequest)

      if (response.data.success) {
        onCheckout(response.data)
      } else {
        throw new Error('Error procesando el pedido')
      }

    } catch (error) {
      console.error('Error en checkout:', error)
      alert('Error procesando el pedido. Por favor intenta nuevamente.')
    } finally {
      setIsCheckingOut(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="cart-overlay">
      <div className="cart-sidebar">
        <div className="cart-header">
          <h2>🛒 Carrito de Compras</h2>
          <button className="close-btn" onClick={onClose}>
            ✕
          </button>
        </div>

        <div className="cart-content">
          {cartItems.length === 0 ? (
            <div className="empty-cart">
              <div className="empty-cart-icon">🛒</div>
              <h3>Tu carrito está vacío</h3>
              <p>Agrega algunos lentes increíbles a tu carrito</p>
            </div>
          ) : (
            <>
              {/* Items del carrito */}
              <div className="cart-items">
                {cartItems.map(item => (
                  <div key={item.cartId} className="cart-item">
                    <div className="item-image">
                      <img src={item.image} alt={item.name} />
                    </div>
                    <div className="item-details">
                      <h4>{item.name}</h4>
                      <p className="item-brand">{item.brand}</p>
                      <div className="item-color">
                        <span>Color: </span>
                        <span 
                          className="color-indicator"
                          style={{ backgroundColor: item.selectedColor }}
                        />
                        <span>{item.selectedColor}</span>
                      </div>
                      <div className="item-price">${item.price}</div>
                    </div>
                    <div className="item-controls">
                      <div className="quantity-controls">
                        <button 
                          onClick={() => onUpdateQuantity(item.cartId, item.quantity - 1)}
                          disabled={item.quantity <= 1}
                        >
                          -
                        </button>
                        <span>{item.quantity}</span>
                        <button 
                          onClick={() => onUpdateQuantity(item.cartId, item.quantity + 1)}
                        >
                          +
                        </button>
                      </div>
                      <button 
                        className="remove-btn"
                        onClick={() => onRemoveItem(item.cartId)}
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Resumen de precios */}
              <div className="cart-summary">
                <div className="summary-line">
                  <span>Subtotal:</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>
                <div className="summary-line">
                  <span>Envío:</span>
                  <span>{shippingCost === 0 ? 'GRATIS' : `$${shippingCost.toFixed(2)}`}</span>
                </div>
                <div className="summary-line">
                  <span>Impuestos:</span>
                  <span>${tax.toFixed(2)}</span>
                </div>
                <div className="summary-line total">
                  <span>Total:</span>
                  <span>${total.toFixed(2)}</span>
                </div>
                
                {subtotal < 50 && (
                  <div className="shipping-notice">
                    💡 Agrega ${(50 - subtotal).toFixed(2)} más para envío GRATIS
                  </div>
                )}
              </div>

              {/* Información de envío */}
              <div className="shipping-form">
                <h3>Información de Envío</h3>
                <div className="form-grid">
                  <input
                    type="text"
                    placeholder="Nombre completo"
                    value={shippingInfo.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                  />
                  <input
                    type="email"
                    placeholder="Email"
                    value={shippingInfo.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                  />
                  <input
                    type="tel"
                    placeholder="Teléfono"
                    value={shippingInfo.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                  />
                  <input
                    type="text"
                    placeholder="Dirección"
                    value={shippingInfo.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    className="full-width"
                  />
                  <input
                    type="text"
                    placeholder="Ciudad"
                    value={shippingInfo.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                  />
                  <input
                    type="text"
                    placeholder="Código Postal"
                    value={shippingInfo.zipCode}
                    onChange={(e) => handleInputChange('zipCode', e.target.value)}
                  />
                </div>

                <div className="shipping-methods">
                  <h4>Método de Envío</h4>
                  {shippingMethods.map(method => (
                    <label key={method.id} className="shipping-option">
                      <input
                        type="radio"
                        name="shipping"
                        value={method.id}
                        checked={shippingInfo.shippingMethod === method.id}
                        onChange={(e) => handleInputChange('shippingMethod', e.target.value)}
                      />
                      <div className="shipping-details">
                        <span className="shipping-name">{method.name}</span>
                        <span className="shipping-time">{method.time}</span>
                        <span className="shipping-cost">
                          {subtotal > 50 && method.id === 'standard' ? 'GRATIS' : `$${method.cost}`}
                        </span>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Botón de checkout */}
              <button 
                className={`checkout-btn ${isCheckingOut ? 'loading' : ''}`}
                onClick={handleCheckout}
                disabled={isCheckingOut || cartItems.length === 0}
              >
                {isCheckingOut ? (
                  <>
                    <span className="loading-spinner"></span>
                    Procesando...
                  </>
                ) : (
                  <>
                    💳 Finalizar Compra - ${total.toFixed(2)}
                  </>
                )}
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default ShoppingCart
