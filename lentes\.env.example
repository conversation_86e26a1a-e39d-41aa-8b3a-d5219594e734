# Archivo de ejemplo de variables de entorno
# Copia este archivo como .env y ajusta los valores según tu configuración

# Configuración del Backend
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000
BACKEND_RELOAD=true

# Configuración del Frontend
FRONTEND_PORT=3000

# Configuración de MediaPipe
DETECTION_CONFIDENCE=0.5
TRACKING_CONFIDENCE=0.5

# Configuración de archivos
MAX_FILE_SIZE=10485760  # 10MB en bytes
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp

# Configuración de CORS
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:3001,http://127.0.0.1:3001

# Configuración de logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Configuración de directorios
STATIC_DIR=static
LENSES_DIR=static/lenses
TEMP_DIR=static/temp
