#!/usr/bin/env python3
"""
Script para crear marcos de lentes realistas usando PIL.
"""

from PIL import Image, ImageDraw
import os

def create_lens_frame(width, height, frame_type, color, filename):
    """Crea un marco de lentes realista."""
    # Crear imagen transparente
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Colores
    colors = {
        'black': (0, 0, 0, 255),
        'brown': (139, 69, 19, 255),
        'blue': (0, 100, 200, 255),
        'red': (200, 0, 0, 255),
        'green': (0, 150, 0, 255),
        'gray': (128, 128, 128, 255),
        'gold': (255, 215, 0, 255),
        'silver': (192, 192, 192, 255)
    }
    
    frame_color = colors.get(color, colors['black'])
    
    # Dimensiones del marco
    center_x = width // 2
    center_y = height // 2
    lens_radius = min(width, height) // 4
    frame_thickness = max(3, lens_radius // 8)
    
    if frame_type == 'glasses':
        # Lentes normales - círculos
        left_center = (center_x - lens_radius - 10, center_y)
        right_center = (center_x + lens_radius + 10, center_y)
        
        # Lentes izquierdo y derecho
        draw.ellipse([left_center[0] - lens_radius, left_center[1] - lens_radius,
                     left_center[0] + lens_radius, left_center[1] + lens_radius],
                    outline=frame_color, width=frame_thickness)
        
        draw.ellipse([right_center[0] - lens_radius, right_center[1] - lens_radius,
                     right_center[0] + lens_radius, right_center[1] + lens_radius],
                    outline=frame_color, width=frame_thickness)
        
        # Puente nasal
        draw.line([left_center[0] + lens_radius, left_center[1],
                  right_center[0] - lens_radius, right_center[1]],
                 fill=frame_color, width=frame_thickness)
        
        # Patillas
        draw.line([left_center[0] - lens_radius, left_center[1],
                  left_center[0] - lens_radius - 30, left_center[1]],
                 fill=frame_color, width=frame_thickness)
        
        draw.line([right_center[0] + lens_radius, right_center[1],
                  right_center[0] + lens_radius + 30, right_center[1]],
                 fill=frame_color, width=frame_thickness)
    
    elif frame_type == 'sunglasses':
        # Lentes de sol - más grandes y oscuros
        lens_radius = int(lens_radius * 1.2)
        left_center = (center_x - lens_radius - 8, center_y)
        right_center = (center_x + lens_radius + 8, center_y)
        
        # Lentes con relleno oscuro
        draw.ellipse([left_center[0] - lens_radius, left_center[1] - lens_radius,
                     left_center[0] + lens_radius, left_center[1] + lens_radius],
                    fill=(50, 50, 50, 180), outline=frame_color, width=frame_thickness)
        
        draw.ellipse([right_center[0] - lens_radius, right_center[1] - lens_radius,
                     right_center[0] + lens_radius, right_center[1] + lens_radius],
                    fill=(50, 50, 50, 180), outline=frame_color, width=frame_thickness)
        
        # Puente nasal más grueso
        draw.line([left_center[0] + lens_radius, left_center[1],
                  right_center[0] - lens_radius, right_center[1]],
                 fill=frame_color, width=frame_thickness + 2)
        
        # Patillas más gruesas
        draw.line([left_center[0] - lens_radius, left_center[1],
                  left_center[0] - lens_radius - 35, left_center[1]],
                 fill=frame_color, width=frame_thickness + 1)
        
        draw.line([right_center[0] + lens_radius, right_center[1],
                  right_center[0] + lens_radius + 35, right_center[1]],
                 fill=frame_color, width=frame_thickness + 1)
    
    elif frame_type == 'reading_glasses':
        # Lentes de lectura - rectangulares
        lens_width = lens_radius
        lens_height = int(lens_radius * 0.8)
        left_center = (center_x - lens_width - 8, center_y)
        right_center = (center_x + lens_width + 8, center_y)
        
        # Marcos rectangulares
        draw.rectangle([left_center[0] - lens_width, left_center[1] - lens_height,
                       left_center[0] + lens_width, left_center[1] + lens_height],
                      outline=frame_color, width=frame_thickness)
        
        draw.rectangle([right_center[0] - lens_width, right_center[1] - lens_height,
                       right_center[0] + lens_width, right_center[1] + lens_height],
                      outline=frame_color, width=frame_thickness)
        
        # Puente nasal
        draw.line([left_center[0] + lens_width, left_center[1],
                  right_center[0] - lens_width, right_center[1]],
                 fill=frame_color, width=frame_thickness)
        
        # Patillas delgadas
        draw.line([left_center[0] - lens_width, left_center[1],
                  left_center[0] - lens_width - 25, left_center[1]],
                 fill=frame_color, width=frame_thickness - 1)
        
        draw.line([right_center[0] + lens_width, right_center[1],
                  right_center[0] + lens_width + 25, right_center[1]],
                 fill=frame_color, width=frame_thickness - 1)
    
    # Guardar imagen
    img.save(filename, 'PNG')
    print(f"Creado: {filename}")

def main():
    """Crear todos los marcos de lentes."""
    # Crear directorio si no existe
    os.makedirs('static/lenses', exist_ok=True)
    
    frame_types = ['glasses', 'sunglasses', 'reading_glasses']
    colors = ['black', 'brown', 'blue', 'red', 'green', 'gray', 'gold', 'silver']
    
    for frame_type in frame_types:
        for color in colors:
            filename = f"static/lenses/{frame_type}_{color}.png"
            create_lens_frame(300, 200, frame_type, color, filename)
    
    print("¡Todos los marcos de lentes han sido creados!")

if __name__ == "__main__":
    main()
