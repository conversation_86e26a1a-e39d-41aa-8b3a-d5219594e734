# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/core/proto/inference_subgraph.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2
from mediapipe.tasks.cc.core.proto import base_options_pb2 as mediapipe_dot_tasks_dot_cc_dot_core_dot_proto_dot_base__options__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n6mediapipe/tasks/cc/core/proto/inference_subgraph.proto\x12\x1amediapipe.tasks.core.proto\x1a$mediapipe/framework/calculator.proto\x1a\x30mediapipe/tasks/cc/core/proto/base_options.proto\"\xdb\x01\n\x18InferenceSubgraphOptions\x12=\n\x0c\x62\x61se_options\x18\x01 \x01(\x0b\x32\'.mediapipe.tasks.core.proto.BaseOptions\x12\x1b\n\x13model_resources_tag\x18\x02 \x01(\t2c\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xbd\xe2\xd8\xd2\x01 \x01(\x0b\x32\x34.mediapipe.tasks.core.proto.InferenceSubgraphOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.core.proto.inference_subgraph_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_INFERENCESUBGRAPHOPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_INFERENCESUBGRAPHOPTIONS']._serialized_start=175
  _globals['_INFERENCESUBGRAPHOPTIONS']._serialized_end=394
# @@protoc_insertion_point(module_scope)
