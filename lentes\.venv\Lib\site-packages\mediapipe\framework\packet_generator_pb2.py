# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/packet_generator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*mediapipe/framework/packet_generator.proto\x12\tmediapipe\"D\n\x16PacketGeneratorOptions\x12\x1a\n\x0cmerge_fields\x18\x01 \x01(\x08:\x04true*\n\x08\xa0\x9c\x01\x10\x80\x80\x80\x80\x02:\x02\x18\x01\"\xd3\x01\n\x15PacketGeneratorConfig\x12\x18\n\x10packet_generator\x18\x01 \x01(\t\x12\x19\n\x11input_side_packet\x18\x02 \x03(\t\x12\x17\n\x0e\x65xternal_input\x18\xea\x07 \x03(\t\x12\x1a\n\x12output_side_packet\x18\x03 \x03(\t\x12\x18\n\x0f\x65xternal_output\x18\xeb\x07 \x03(\t\x12\x32\n\x07options\x18\x04 \x01(\x0b\x32!.mediapipe.PacketGeneratorOptions:\x02\x18\x01\x42\x32\n\x1a\x63om.google.mediapipe.protoB\x14PacketGeneratorProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.packet_generator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\032com.google.mediapipe.protoB\024PacketGeneratorProto'
  _PACKETGENERATOROPTIONS._options = None
  _PACKETGENERATOROPTIONS._serialized_options = b'\030\001'
  _PACKETGENERATORCONFIG._options = None
  _PACKETGENERATORCONFIG._serialized_options = b'\030\001'
  _globals['_PACKETGENERATOROPTIONS']._serialized_start=57
  _globals['_PACKETGENERATOROPTIONS']._serialized_end=125
  _globals['_PACKETGENERATORCONFIG']._serialized_start=128
  _globals['_PACKETGENERATORCONFIG']._serialized_end=339
# @@protoc_insertion_point(module_scope)
