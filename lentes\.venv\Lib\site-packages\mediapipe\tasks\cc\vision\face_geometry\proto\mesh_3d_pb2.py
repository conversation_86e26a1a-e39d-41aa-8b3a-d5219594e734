# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/vision/face_geometry/proto/mesh_3d.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n;mediapipe/tasks/cc/vision/face_geometry/proto/mesh_3d.proto\x12*mediapipe.tasks.vision.face_geometry.proto\"\x9f\x02\n\x06Mesh3d\x12R\n\x0bvertex_type\x18\x01 \x01(\x0e\x32=.mediapipe.tasks.vision.face_geometry.proto.Mesh3d.VertexType\x12X\n\x0eprimitive_type\x18\x02 \x01(\x0e\<EMAIL>.face_geometry.proto.Mesh3d.PrimitiveType\x12\x15\n\rvertex_buffer\x18\x03 \x03(\x02\x12\x14\n\x0cindex_buffer\x18\x04 \x03(\r\"\x1b\n\nVertexType\x12\r\n\tVERTEX_PT\x10\x00\"\x1d\n\rPrimitiveType\x12\x0c\n\x08TRIANGLE\x10\x00\x42\x43\n4com.google.mediapipe.tasks.vision.facegeometry.protoB\x0bMesh3DProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.vision.face_geometry.proto.mesh_3d_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n4com.google.mediapipe.tasks.vision.facegeometry.protoB\013Mesh3DProto'
  _globals['_MESH3D']._serialized_start=108
  _globals['_MESH3D']._serialized_end=395
  _globals['_MESH3D_VERTEXTYPE']._serialized_start=337
  _globals['_MESH3D_VERTEXTYPE']._serialized_end=364
  _globals['_MESH3D_PRIMITIVETYPE']._serialized_start=366
  _globals['_MESH3D_PRIMITIVETYPE']._serialized_end=395
# @@protoc_insertion_point(module_scope)
