import React, { useRef, useState, useEffect } from 'react'
import './CameraCapture.css'
import LensGallery from './LensGallery'

const CameraCapture = ({ onImageCapture, lensType, lensColor, onLensChange }) => {
  const videoRef = useRef(null)
  const canvasRef = useRef(null)
  const [isStreaming, setIsStreaming] = useState(false)
  const [error, setError] = useState('')
  const [devices, setDevices] = useState([])
  const [selectedDevice, setSelectedDevice] = useState('')
  const [stream, setStream] = useState(null)
  const [showGallery, setShowGallery] = useState(false)
  const [selectedLensType, setSelectedLensType] = useState('')

  // Obtener dispositivos de cámara disponibles
  useEffect(() => {
    const getDevices = async () => {
      try {
        const deviceList = await navigator.mediaDevices.enumerateDevices()
        const videoDevices = deviceList.filter(device => device.kind === 'videoinput')
        setDevices(videoDevices)
        if (videoDevices.length > 0) {
          setSelectedDevice(videoDevices[0].deviceId)
        }
      } catch (err) {
        console.error('Error obteniendo dispositivos:', err)
        setError('No se pudieron obtener los dispositivos de cámara')
      }
    }

    getDevices()
  }, [])

  // Iniciar cámara
  const startCamera = async () => {
    try {
      setError('')
      
      const constraints = {
        video: {
          deviceId: selectedDevice ? { exact: selectedDevice } : undefined,
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        }
      }

      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints)
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream
        setStream(mediaStream)
        setIsStreaming(true)
      }
    } catch (err) {
      console.error('Error accediendo a la cámara:', err)
      setError('No se pudo acceder a la cámara. Verifica los permisos.')
    }
  }

  // Detener cámara
  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop())
      setStream(null)
    }
    setIsStreaming(false)
  }

  // Capturar imagen
  const captureImage = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current
      const canvas = canvasRef.current
      const context = canvas.getContext('2d')

      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // Dibujar el frame actual del video en el canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height)

      // Convertir a blob
      canvas.toBlob((blob) => {
        if (blob && onImageCapture) {
          const file = new File([blob], 'camera-capture.jpg', { type: 'image/jpeg' })
          onImageCapture(file)
        }
      }, 'image/jpeg', 0.9)
    }
  }

  // Cambiar dispositivo de cámara
  const changeCamera = (deviceId) => {
    setSelectedDevice(deviceId)
    if (isStreaming) {
      stopCamera()
      setTimeout(() => {
        startCamera()
      }, 100)
    }
  }

  // Limpiar al desmontar
  useEffect(() => {
    return () => {
      stopCamera()
    }
  }, [])

  // Manejar clic en tipo de lente para mostrar galería
  const handleLensTypeClick = (type) => {
    setSelectedLensType(type)
    setShowGallery(true)
  }

  // Manejar selección de lente desde la galería
  const handleLensSelect = (type, color) => {
    if (onLensChange) {
      onLensChange(type, color)
    }
    setShowGallery(false)
  }

  const lensTypes = [
    { value: 'glasses', label: 'Lentes Normales', icon: 'glasses' },
    { value: 'sunglasses', label: 'Lentes de Sol', icon: 'sunglasses' },
    { value: 'reading_glasses', label: 'Lentes de Lectura', icon: 'reading' },
    { value: 'safety_glasses', label: 'Lentes de Seguridad', icon: 'safety' },
    { value: 'fashion_glasses', label: 'Lentes de Moda', icon: 'fashion' }
  ]

  const getLensIcon = (iconType) => {
    const icons = {
      glasses: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" fill="currentColor"/>
        </svg>
      ),
      sunglasses: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7z" fill="currentColor"/>
        </svg>
      ),
      reading: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M21 5c-1.11-.35-2.33-.5-3.5-.5-1.95 0-4.05.4-5.5 1.5-1.45-1.1-3.55-1.5-5.5-1.5S2.45 4.9 1 6v14.65c0 .25.25.5.5.5.1 0 .15-.05.25-.05C3.1 20.45 5.05 20 6.5 20c1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.3 4.75 1.05.1.05.15.05.25.05.25 0 .5-.25.5-.5V6c-.6-.45-1.25-.75-2-1z" fill="currentColor"/>
        </svg>
      ),
      safety: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10.1V11.1C15.4,11.4 16,12 16,12.8V16.7C16,17.4 15.4,18 14.7,18H9.2C8.6,18 8,17.4 8,16.8V12.8C8,12 8.4,11.4 9,11.1V10.1C9,8.6 10.6,7 12,7Z" fill="currentColor"/>
        </svg>
      ),
      fashion: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
        </svg>
      )
    }
    return icons[iconType] || icons.glasses
  }

  const colors = [
    { value: 'blue', label: 'Azul', color: '#0066cc' },
    { value: 'red', label: 'Rojo', color: '#cc0000' },
    { value: 'green', label: 'Verde', color: '#00cc00' },
    { value: 'black', label: 'Negro', color: '#000000' },
    { value: 'brown', label: 'Marrón', color: '#8B4513' },
    { value: 'gray', label: 'Gris', color: '#808080' },
    { value: 'gold', label: 'Dorado', color: '#FFD700' },
    { value: 'silver', label: 'Plateado', color: '#C0C0C0' }
  ]

  return (
    <div className="camera-capture">
      <div className="camera-section">
        <div className="camera-container">
          {error && (
            <div className="error-message">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z" fill="currentColor"/>
              </svg>
              {error}
            </div>
          )}
          
          <div className="video-container">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className={`camera-video ${isStreaming ? 'active' : ''}`}
            />
            {!isStreaming && (
              <div className="camera-placeholder">
                <div className="placeholder-content">
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 15.5c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z" fill="currentColor"/>
                    <path d="M9 2L7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2H9zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" fill="currentColor"/>
                  </svg>
                  <h3>Prueba Virtual de Lentes</h3>
                  <p>Activa tu cámara para probar lentes en tiempo real</p>
                </div>
              </div>
            )}
          </div>

          <canvas ref={canvasRef} style={{ display: 'none' }} />

          <div className="camera-controls">
            {devices.length > 1 && (
              <select
                value={selectedDevice}
                onChange={(e) => changeCamera(e.target.value)}
                className="camera-select"
              >
                {devices.map((device, index) => (
                  <option key={device.deviceId} value={device.deviceId}>
                    {device.label || `Cámara ${index + 1}`}
                  </option>
                ))}
              </select>
            )}

            <div className="control-buttons">
              {!isStreaming ? (
                <button onClick={startCamera} className="btn-primary">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" fill="currentColor"/>
                  </svg>
                  Activar Cámara
                </button>
              ) : (
                <>
                  <button onClick={captureImage} className="btn-capture">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 15.5c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z" fill="currentColor"/>
                      <path d="M9 2L7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2H9zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z" fill="currentColor"/>
                    </svg>
                    Capturar y Probar
                  </button>
                  <button onClick={stopCamera} className="btn-secondary">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6 6h12v12H6z" fill="currentColor"/>
                    </svg>
                    Detener
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="lens-controls">
          <div className="control-group">
            <h4>Tipo de Lente</h4>
            <div className="lens-type-grid">
              {lensTypes.map(type => (
                <button
                  key={type.value}
                  className={`lens-type-btn ${lensType === type.value ? 'active' : ''}`}
                  onClick={() => handleLensTypeClick(type.value)}
                >
                  <span className="lens-icon">{getLensIcon(type.icon)}</span>
                  <span className="lens-label">{type.label}</span>
                  <span className="gallery-indicator">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 18l6-6-6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </span>
                </button>
              ))}
            </div>
          </div>

          <div className="control-group">
            <h4>Color del Marco</h4>
            <div className="color-palette">
              {colors.map(color => (
                <button
                  key={color.value}
                  className={`color-btn ${lensColor === color.value ? 'active' : ''}`}
                  style={{ backgroundColor: color.color }}
                  onClick={() => onLensChange && onLensChange(lensType, color.value)}
                  title={color.label}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Galería de Lentes */}
      <LensGallery
        isOpen={showGallery}
        onClose={() => setShowGallery(false)}
        lensType={selectedLensType}
        onLensSelect={handleLensSelect}
        currentLensColor={lensColor}
      />
    </div>
  )
}

export default CameraCapture
