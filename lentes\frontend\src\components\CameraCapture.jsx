import React, { useRef, useState, useEffect } from 'react'
import './CameraCapture.css'

const CameraCapture = ({ onImageCapture, lensType, lensColor, onLensChange }) => {
  const videoRef = useRef(null)
  const canvasRef = useRef(null)
  const [isStreaming, setIsStreaming] = useState(false)
  const [error, setError] = useState('')
  const [devices, setDevices] = useState([])
  const [selectedDevice, setSelectedDevice] = useState('')
  const [stream, setStream] = useState(null)

  // Obtener dispositivos de cámara disponibles
  useEffect(() => {
    const getDevices = async () => {
      try {
        const deviceList = await navigator.mediaDevices.enumerateDevices()
        const videoDevices = deviceList.filter(device => device.kind === 'videoinput')
        setDevices(videoDevices)
        if (videoDevices.length > 0) {
          setSelectedDevice(videoDevices[0].deviceId)
        }
      } catch (err) {
        console.error('Error obteniendo dispositivos:', err)
        setError('No se pudieron obtener los dispositivos de cámara')
      }
    }

    getDevices()
  }, [])

  // Iniciar cámara
  const startCamera = async () => {
    try {
      setError('')
      
      const constraints = {
        video: {
          deviceId: selectedDevice ? { exact: selectedDevice } : undefined,
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        }
      }

      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints)
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream
        setStream(mediaStream)
        setIsStreaming(true)
      }
    } catch (err) {
      console.error('Error accediendo a la cámara:', err)
      setError('No se pudo acceder a la cámara. Verifica los permisos.')
    }
  }

  // Detener cámara
  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop())
      setStream(null)
    }
    setIsStreaming(false)
  }

  // Capturar imagen
  const captureImage = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current
      const canvas = canvasRef.current
      const context = canvas.getContext('2d')

      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // Dibujar el frame actual del video en el canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height)

      // Convertir a blob
      canvas.toBlob((blob) => {
        if (blob && onImageCapture) {
          const file = new File([blob], 'camera-capture.jpg', { type: 'image/jpeg' })
          onImageCapture(file)
        }
      }, 'image/jpeg', 0.9)
    }
  }

  // Cambiar dispositivo de cámara
  const changeCamera = (deviceId) => {
    setSelectedDevice(deviceId)
    if (isStreaming) {
      stopCamera()
      setTimeout(() => {
        startCamera()
      }, 100)
    }
  }

  // Limpiar al desmontar
  useEffect(() => {
    return () => {
      stopCamera()
    }
  }, [])

  const lensTypes = [
    { value: 'glasses', label: 'Lentes Normales', icon: '👓' },
    { value: 'sunglasses', label: 'Lentes de Sol', icon: '🕶️' },
    { value: 'reading_glasses', label: 'Lentes de Lectura', icon: '🤓' },
    { value: 'safety_glasses', label: 'Lentes de Seguridad', icon: '🥽' },
    { value: 'fashion_glasses', label: 'Lentes de Moda', icon: '👓' }
  ]

  const colors = [
    { value: 'blue', label: 'Azul', color: '#0066cc' },
    { value: 'red', label: 'Rojo', color: '#cc0000' },
    { value: 'green', label: 'Verde', color: '#00cc00' },
    { value: 'black', label: 'Negro', color: '#000000' },
    { value: 'brown', label: 'Marrón', color: '#8B4513' },
    { value: 'gray', label: 'Gris', color: '#808080' },
    { value: 'gold', label: 'Dorado', color: '#FFD700' },
    { value: 'silver', label: 'Plateado', color: '#C0C0C0' }
  ]

  return (
    <div className="camera-capture">
      <div className="camera-section">
        <div className="camera-container">
          {error && (
            <div className="error-message">
              <span className="error-icon">⚠️</span>
              {error}
            </div>
          )}
          
          <div className="video-container">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className={`camera-video ${isStreaming ? 'active' : ''}`}
            />
            {!isStreaming && (
              <div className="camera-placeholder">
                <div className="placeholder-content">
                  <span className="camera-icon">📷</span>
                  <h3>Prueba Virtual de Lentes</h3>
                  <p>Activa tu cámara para probar lentes en tiempo real</p>
                </div>
              </div>
            )}
          </div>

          <canvas ref={canvasRef} style={{ display: 'none' }} />

          <div className="camera-controls">
            {devices.length > 1 && (
              <select
                value={selectedDevice}
                onChange={(e) => changeCamera(e.target.value)}
                className="camera-select"
              >
                {devices.map((device, index) => (
                  <option key={device.deviceId} value={device.deviceId}>
                    {device.label || `Cámara ${index + 1}`}
                  </option>
                ))}
              </select>
            )}

            <div className="control-buttons">
              {!isStreaming ? (
                <button onClick={startCamera} className="btn-primary">
                  <span className="btn-icon">📹</span>
                  Activar Cámara
                </button>
              ) : (
                <>
                  <button onClick={captureImage} className="btn-capture">
                    <span className="btn-icon">📸</span>
                    Capturar y Probar
                  </button>
                  <button onClick={stopCamera} className="btn-secondary">
                    <span className="btn-icon">⏹️</span>
                    Detener
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="lens-controls">
          <div className="control-group">
            <h4>Tipo de Lente</h4>
            <div className="lens-type-grid">
              {lensTypes.map(type => (
                <button
                  key={type.value}
                  className={`lens-type-btn ${lensType === type.value ? 'active' : ''}`}
                  onClick={() => onLensChange && onLensChange(type.value, lensColor)}
                >
                  <span className="lens-icon">{type.icon}</span>
                  <span className="lens-label">{type.label}</span>
                </button>
              ))}
            </div>
          </div>

          <div className="control-group">
            <h4>Color del Marco</h4>
            <div className="color-palette">
              {colors.map(color => (
                <button
                  key={color.value}
                  className={`color-btn ${lensColor === color.value ? 'active' : ''}`}
                  style={{ backgroundColor: color.color }}
                  onClick={() => onLensChange && onLensChange(lensType, color.value)}
                  title={color.label}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CameraCapture
