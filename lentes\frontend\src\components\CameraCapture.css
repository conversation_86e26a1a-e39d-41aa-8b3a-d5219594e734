/* Estilos para el componente de captura de cámara */

.camera-capture {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.camera-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: start;
}

.camera-container {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.video-container {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 15px;
  overflow: hidden;
  background: #f8f9fa;
  margin-bottom: 1.5rem;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.camera-video.active {
  box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);
}

.camera-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.placeholder-content {
  text-align: center;
}

.camera-icon {
  font-size: 4rem;
  display: block;
  margin-bottom: 1rem;
}

.placeholder-content h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.placeholder-content p {
  font-size: 1rem;
  opacity: 0.9;
}

.camera-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.camera-select {
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  font-size: 1rem;
  background: white;
  transition: border-color 0.3s ease;
}

.camera-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.control-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.btn-primary, .btn-secondary, .btn-capture {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.btn-capture {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 184, 148, 0.4);
}

.btn-capture:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 184, 148, 0.6);
}

.btn-secondary {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.btn-icon {
  font-size: 1.2rem;
}

.lens-controls {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  height: fit-content;
}

.control-group {
  margin-bottom: 2rem;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-group h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1rem;
  text-align: center;
}

.lens-type-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.lens-type-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.lens-type-btn:hover {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.lens-type-btn.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.lens-icon {
  font-size: 1.5rem;
  min-width: 2rem;
}

.lens-label {
  font-weight: 500;
  font-size: 0.95rem;
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
}

.color-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.color-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.color-btn.active {
  border-color: #667eea;
  transform: scale(1.15);
  box-shadow: 0 0 0 2px white, 0 0 0 4px #667eea, 0 4px 12px rgba(0, 0, 0, 0.25);
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 1rem;
  border-radius: 10px;
  margin-bottom: 1rem;
  font-weight: 500;
}

.error-icon {
  font-size: 1.2rem;
}

/* Responsive design */
@media (max-width: 1024px) {
  .camera-section {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .lens-type-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .camera-capture {
    padding: 1rem;
  }
  
  .camera-container,
  .lens-controls {
    padding: 1.5rem;
  }
  
  .video-container {
    height: 300px;
  }
  
  .control-buttons {
    flex-direction: column;
  }
  
  .lens-type-grid {
    grid-template-columns: 1fr;
  }
  
  .color-palette {
    grid-template-columns: repeat(4, 1fr);
    justify-items: center;
  }
  
  .color-btn {
    width: 40px;
    height: 40px;
  }
}
