import React, { useState, useRef, useEffect } from 'react'
import axios from 'axios'
import './App.css'
import CameraCapture from './components/CameraCapture'
import ProductCatalog from './components/ProductCatalog'
import ShoppingCart from './components/ShoppingCart'
import OrderTracking from './components/OrderTracking'
import LegalPages from './components/LegalPages'
import Header from './components/Header'
import Footer from './components/Footer'

function App() {
  const [selectedImage, setSelectedImage] = useState(null)
  const [processedImage, setProcessedImage] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [lensType, setLensType] = useState('glasses')
  const [lensColor, setLensColor] = useState('blue')
  const [facesDetected, setFacesDetected] = useState(0)
  const [currentView, setCurrentView] = useState('camera') // camera, catalog, cart
  const [cartItems, setCartItems] = useState([])
  const [isCartOpen, setIsCartOpen] = useState(false)
  const fileInputRef = useRef(null)

  const lensTypes = [
    { value: 'glasses', label: 'Lentes Normales' },
    { value: 'sunglasses', label: 'Lentes de Sol' },
    { value: 'reading_glasses', label: 'Lentes de Lectura' },
    { value: 'safety_glasses', label: 'Lentes de Seguridad' },
    { value: 'fashion_glasses', label: 'Lentes de Moda' }
  ]

  const colors = [
    { value: 'blue', label: 'Azul', color: '#0066cc' },
    { value: 'red', label: 'Rojo', color: '#cc0000' },
    { value: 'green', label: 'Verde', color: '#00cc00' },
    { value: 'black', label: 'Negro', color: '#000000' },
    { value: 'brown', label: 'Marrón', color: '#8B4513' },
    { value: 'gray', label: 'Gris', color: '#808080' },
    { value: 'gold', label: 'Dorado', color: '#FFD700' },
    { value: 'silver', label: 'Plateado', color: '#C0C0C0' }
  ]

  const handleImageSelect = (event) => {
    const file = event.target.files[0]
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        setError('El archivo es demasiado grande. Máximo 10MB.')
        return
      }
      
      if (!file.type.startsWith('image/')) {
        setError('Por favor selecciona un archivo de imagen válido.')
        return
      }

      setSelectedImage(file)
      setProcessedImage(null)
      setError('')
      setFacesDetected(0)
    }
  }



  const handleReset = () => {
    setSelectedImage(null)
    setProcessedImage(null)
    setError('')
    setFacesDetected(0)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const downloadImage = () => {
    if (processedImage) {
      const link = document.createElement('a')
      link.href = processedImage
      link.download = `lentes_${lensType}_${lensColor}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  // Funciones del carrito
  const addToCart = (product) => {
    const existingItem = cartItems.find(item => item.cartId === product.cartId)

    if (existingItem) {
      updateCartQuantity(product.cartId, existingItem.quantity + 1)
    } else {
      setCartItems(prev => [...prev, product])
    }
  }

  const updateCartQuantity = (cartId, newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(cartId)
      return
    }

    setCartItems(prev =>
      prev.map(item =>
        item.cartId === cartId
          ? { ...item, quantity: newQuantity }
          : item
      )
    )
  }

  const removeFromCart = (cartId) => {
    setCartItems(prev => prev.filter(item => item.cartId !== cartId))
  }

  const handleCheckout = (orderResponse) => {
    console.log('Orden procesada:', orderResponse)

    if (orderResponse.success && orderResponse.order) {
      setCartItems([])
      setIsCartOpen(false)

      // Mostrar mensaje de éxito con información del pedido
      const orderNumber = orderResponse.order.order_number
      const estimatedDelivery = orderResponse.estimated_delivery

      alert(`¡Pedido ${orderNumber} procesado exitosamente!\n\nEntrega estimada: ${estimatedDelivery}\n\nPuedes rastrear tu pedido en la sección de Seguimiento.`)

      // Cambiar a la vista de seguimiento
      setCurrentView('tracking')
    } else {
      alert('Error procesando el pedido. Por favor intenta nuevamente.')
    }
  }

  const handleTryVirtual = (product) => {
    setLensType(product.category)
    setCurrentView('camera')
  }

  const handleLensChange = (type, color) => {
    setLensType(type)
    setLensColor(color)
  }

  const handleImageCapture = (imageFile) => {
    setSelectedImage(imageFile)
    handleProcessImage(imageFile)
  }

  const handleProcessImage = async (imageFile = selectedImage) => {
    if (!imageFile) return

    setLoading(true)
    setError('')

    try {
      const formData = new FormData()
      formData.append('image', imageFile)
      formData.append('lens_type', lensType)
      formData.append('lens_color', lensColor)

      const response = await axios.post(
        '/api/v1/lens/apply-stream',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          responseType: 'blob'
        }
      )

      const imageUrl = URL.createObjectURL(response.data)
      setProcessedImage(imageUrl)

      const facesCount = response.headers['x-faces-detected']
      if (facesCount) {
        setFacesDetected(parseInt(facesCount))
      }

    } catch (err) {
      console.error('Error procesando imagen:', err)
      if (err.response?.status === 404) {
        setError('No se detectaron rostros en la imagen. Intenta con otra imagen.')
      } else if (err.response?.status === 400) {
        setError('Formato de imagen no válido o archivo demasiado grande.')
      } else {
        setError('Error procesando la imagen. Intenta nuevamente.')
      }
    } finally {
      setLoading(false)
    }
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'camera':
        return (
          <CameraCapture
            onImageCapture={handleImageCapture}
            lensType={lensType}
            lensColor={lensColor}
            onLensChange={handleLensChange}
          />
        )
      case 'catalog':
        return (
          <ProductCatalog
            onAddToCart={addToCart}
            onTryVirtual={handleTryVirtual}
          />
        )
      case 'tracking':
        return <OrderTracking />
      case 'privacy':
        return <LegalPages page="privacy" />
      case 'terms':
        return <LegalPages page="terms" />
      case 'cookies':
        return <LegalPages page="cookies" />
      case 'accessibility':
        return <LegalPages page="accessibility" />
      case 'about':
        return (
          <div className="info-page">
            <h2>Sobre Nosotros</h2>
            <div className="about-content">
              <p>OpticaVirtual es la plataforma líder en prueba virtual de lentes, combinando tecnología de inteligencia artificial con la experiencia de compra de óptica tradicional.</p>
              <h3>Nuestra Misión</h3>
              <p>Revolucionar la forma en que las personas compran lentes, ofreciendo una experiencia virtual inmersiva que permite probar productos desde la comodidad del hogar.</p>
              <h3>Tecnología Avanzada</h3>
              <p>Utilizamos algoritmos de visión por computadora y detección facial para proporcionar una experiencia de prueba virtual precisa y realista.</p>
            </div>
          </div>
        )
      case 'contact':
        return (
          <div className="info-page">
            <h2>Contacto</h2>
            <div className="contact-content">
              <div className="contact-methods">
                <div className="contact-method">
                  <h3>Atención al Cliente</h3>
                  <p>Teléfono: +****************</p>
                  <p>Email: <EMAIL></p>
                  <p>Horario: Lunes a Viernes, 9:00 AM - 6:00 PM</p>
                </div>
                <div className="contact-method">
                  <h3>Ventas</h3>
                  <p>Teléfono: +****************</p>
                  <p>Email: <EMAIL></p>
                </div>
                <div className="contact-method">
                  <h3>Soporte Técnico</h3>
                  <p>Email: <EMAIL></p>
                  <p>Chat en vivo disponible 24/7</p>
                </div>
              </div>
            </div>
          </div>
        )
      default:
        return (
          <CameraCapture
            onImageCapture={handleImageCapture}
            lensType={lensType}
            lensColor={lensColor}
            onLensChange={handleLensChange}
          />
        )
    }
  }

  return (
    <div className="App">
      <Header
        currentView={currentView}
        onViewChange={setCurrentView}
        cartItems={cartItems}
        onCartToggle={() => setIsCartOpen(!isCartOpen)}
      />

      <main className="app-main">
        {renderCurrentView()}

        {/* Mostrar resultados de procesamiento si están disponibles */}
        {processedImage && currentView === 'camera' && (
          <div className="results-section">
            <div className="result-container">
              <h3>Resultado</h3>
              <img
                src={processedImage}
                alt="Imagen con lentes"
                className="result-image"
              />
              {facesDetected > 0 && (
                <p className="faces-info">
                  👥 {facesDetected} rostro{facesDetected > 1 ? 's' : ''} detectado{facesDetected > 1 ? 's' : ''}
                </p>
              )}
              <button
                onClick={() => {
                  const link = document.createElement('a')
                  link.href = processedImage
                  link.download = `lentes_${lensType}_${lensColor}.png`
                  document.body.appendChild(link)
                  link.click()
                  document.body.removeChild(link)
                }}
                className="download-button"
              >
                💾 Descargar Imagen
              </button>
            </div>
          </div>
        )}

        {error && (
          <div className="error-message">
            ⚠️ {error}
          </div>
        )}
      </main>

      <ShoppingCart
        isOpen={isCartOpen}
        onClose={() => setIsCartOpen(false)}
        cartItems={cartItems}
        onUpdateQuantity={updateCartQuantity}
        onRemoveItem={removeFromCart}
        onCheckout={handleCheckout}
      />

      <Footer onNavigate={setCurrentView} />
    </div>
  )
}

export default App
