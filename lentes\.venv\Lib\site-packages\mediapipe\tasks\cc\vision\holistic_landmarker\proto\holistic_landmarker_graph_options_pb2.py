# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/vision/holistic_landmarker/proto/holistic_landmarker_graph_options.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.tasks.cc.core.proto import base_options_pb2 as mediapipe_dot_tasks_dot_cc_dot_core_dot_proto_dot_base__options__pb2
from mediapipe.tasks.cc.vision.face_detector.proto import face_detector_graph_options_pb2 as mediapipe_dot_tasks_dot_cc_dot_vision_dot_face__detector_dot_proto_dot_face__detector__graph__options__pb2
from mediapipe.tasks.cc.vision.face_landmarker.proto import face_landmarks_detector_graph_options_pb2 as mediapipe_dot_tasks_dot_cc_dot_vision_dot_face__landmarker_dot_proto_dot_face__landmarks__detector__graph__options__pb2
from mediapipe.tasks.cc.vision.hand_landmarker.proto import hand_landmarks_detector_graph_options_pb2 as mediapipe_dot_tasks_dot_cc_dot_vision_dot_hand__landmarker_dot_proto_dot_hand__landmarks__detector__graph__options__pb2
from mediapipe.tasks.cc.vision.hand_landmarker.proto import hand_roi_refinement_graph_options_pb2 as mediapipe_dot_tasks_dot_cc_dot_vision_dot_hand__landmarker_dot_proto_dot_hand__roi__refinement__graph__options__pb2
from mediapipe.tasks.cc.vision.pose_detector.proto import pose_detector_graph_options_pb2 as mediapipe_dot_tasks_dot_cc_dot_vision_dot_pose__detector_dot_proto_dot_pose__detector__graph__options__pb2
from mediapipe.tasks.cc.vision.pose_landmarker.proto import pose_landmarks_detector_graph_options_pb2 as mediapipe_dot_tasks_dot_cc_dot_vision_dot_pose__landmarker_dot_proto_dot_pose__landmarks__detector__graph__options__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n[mediapipe/tasks/cc/vision/holistic_landmarker/proto/holistic_landmarker_graph_options.proto\x12\x30mediapipe.tasks.vision.holistic_landmarker.proto\x1a\x30mediapipe/tasks/cc/core/proto/base_options.proto\x1aOmediapipe/tasks/cc/vision/face_detector/proto/face_detector_graph_options.proto\x1a[mediapipe/tasks/cc/vision/face_landmarker/proto/face_landmarks_detector_graph_options.proto\x1a[mediapipe/tasks/cc/vision/hand_landmarker/proto/hand_landmarks_detector_graph_options.proto\x1aWmediapipe/tasks/cc/vision/hand_landmarker/proto/hand_roi_refinement_graph_options.proto\x1aOmediapipe/tasks/cc/vision/pose_detector/proto/pose_detector_graph_options.proto\x1a[mediapipe/tasks/cc/vision/pose_landmarker/proto/pose_landmarks_detector_graph_options.proto\"\xad\x06\n\x1eHolisticLandmarkerGraphOptions\x12=\n\x0c\x62\x61se_options\x18\x01 \x01(\x0b\x32\'.mediapipe.tasks.core.proto.BaseOptions\x12~\n%hand_landmarks_detector_graph_options\x18\x02 \x01(\x0b\x32O.mediapipe.tasks.vision.hand_landmarker.proto.HandLandmarksDetectorGraphOptions\x12v\n!hand_roi_refinement_graph_options\x18\x03 \x01(\x0b\x32K.mediapipe.tasks.vision.hand_landmarker.proto.HandRoiRefinementGraphOptions\x12i\n\x1b\x66\x61\x63\x65_detector_graph_options\x18\x04 \x01(\x0b\x32\x44.mediapipe.tasks.vision.face_detector.proto.FaceDetectorGraphOptions\x12~\n%face_landmarks_detector_graph_options\x18\x05 \x01(\x0b\x32O.mediapipe.tasks.vision.face_landmarker.proto.FaceLandmarksDetectorGraphOptions\x12i\n\x1bpose_detector_graph_options\x18\x06 \x01(\x0b\x32\x44.mediapipe.tasks.vision.pose_detector.proto.PoseDetectorGraphOptions\x12~\n%pose_landmarks_detector_graph_options\x18\x07 \x01(\x0b\x32O.mediapipe.tasks.vision.pose_landmarker.proto.PoseLandmarksDetectorGraphOptionsBa\n:com.google.mediapipe.tasks.vision.holisticlandmarker.protoB#HolisticLandmarkerGraphOptionsProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.vision.holistic_landmarker.proto.holistic_landmarker_graph_options_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n:com.google.mediapipe.tasks.vision.holisticlandmarker.protoB#HolisticLandmarkerGraphOptionsProto'
  _globals['_HOLISTICLANDMARKERGRAPHOPTIONS']._serialized_start=726
  _globals['_HOLISTICLANDMARKERGRAPHOPTIONS']._serialized_end=1539
# @@protoc_insertion_point(module_scope)
