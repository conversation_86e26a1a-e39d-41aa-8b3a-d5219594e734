# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/modules/face_geometry/protos/face_geometry.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework.formats import matrix_data_pb2 as mediapipe_dot_framework_dot_formats_dot_matrix__data__pb2
from mediapipe.modules.face_geometry.protos import mesh_3d_pb2 as mediapipe_dot_modules_dot_face__geometry_dot_protos_dot_mesh__3d__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n:mediapipe/modules/face_geometry/protos/face_geometry.proto\x12\x17mediapipe.face_geometry\x1a-mediapipe/framework/formats/matrix_data.proto\x1a\x34mediapipe/modules/face_geometry/protos/mesh_3d.proto\"s\n\x0c\x46\x61\x63\x65Geometry\x12-\n\x04mesh\x18\x01 \x01(\x0b\x32\x1f.mediapipe.face_geometry.Mesh3d\x12\x34\n\x15pose_transform_matrix\x18\x02 \x01(\x0b\x32\x15.mediapipe.MatrixDataB>\n)com.google.mediapipe.modules.facegeometryB\x11\x46\x61\x63\x65GeometryProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.modules.face_geometry.protos.face_geometry_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n)com.google.mediapipe.modules.facegeometryB\021FaceGeometryProto'
  _globals['_FACEGEOMETRY']._serialized_start=188
  _globals['_FACEGEOMETRY']._serialized_end=303
# @@protoc_insertion_point(module_scope)
