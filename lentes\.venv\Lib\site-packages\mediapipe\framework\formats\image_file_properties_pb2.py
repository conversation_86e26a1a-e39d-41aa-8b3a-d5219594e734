# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/formats/image_file_properties.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7mediapipe/framework/formats/image_file_properties.proto\x12\tmediapipe\"\x91\x01\n\x13ImageFileProperties\x12\x13\n\x0bimage_width\x18\x01 \x01(\r\x12\x14\n\x0cimage_height\x18\x02 \x01(\r\x12\x17\n\x0f\x66ocal_length_mm\x18\x03 \x01(\x01\x12\x19\n\x11\x66ocal_length_35mm\x18\x04 \x01(\x01\x12\x1b\n\x13\x66ocal_length_pixels\x18\x05 \x01(\x01')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.formats.image_file_properties_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_IMAGEFILEPROPERTIES']._serialized_start=71
  _globals['_IMAGEFILEPROPERTIES']._serialized_end=216
# @@protoc_insertion_point(module_scope)
