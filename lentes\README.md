# 🕶️ Simulación de Lentes - Aplicación Web

Aplicación web completa para simulación de lentes en tiempo real usando detección de landmarks faciales con MediaPipe y superposición de imágenes con OpenCV.

## 🏗️ Arquitectura

### Backend (FastAPI + Python)
- **FastAPI**: API REST moderna con documentación automática
- **MediaPipe**: Detección de landmarks faciales en tiempo real
- **OpenCV**: Procesamiento de imágenes y transformaciones geométricas
- **Pydantic**: Validación de datos y configuración
- **Structlog**: Logging estructurado en JSON

### Frontend (React + Vite)
- **React 18**: Interfaz de usuario moderna y reactiva
- **Vite**: Build tool rápido para desarrollo
- **Axios**: Cliente HTTP para comunicación con la API

## 🚀 Instalación y Configuración

### Prerrequisitos
- Python 3.9+
- Node.js 18+
- npm o yarn

### 1. Configurar Backend

```bash
# Navegar al directorio backend
cd backend

# Crear entorno virtual
python -m venv venv

# Activar entorno virtual
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Instalar dependencias
pip install -r requirements.txt
```

### 2. Configurar Frontend

```bash
# Navegar al directorio frontend
cd frontend

# Instalar dependencias
npm install
```

## 🎯 Ejecución

## 🚀 Inicio Rápido

### Opción 1: Script Automático (Recomendado)
```bash
# Ejecutar el script de inicio automático
python start_app.py
```

Este script:
- ✅ Verifica todas las dependencias
- 🔧 Instala dependencias faltantes automáticamente
- 🚀 Inicia backend y frontend
- 🌐 Abre el navegador automáticamente
- 📊 Muestra el estado de los servicios

### Opción 2: Inicio Manual

#### Ejecutar Backend
```bash
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

#### Ejecutar Frontend
```bash
cd frontend
npm run dev
```

## 🌐 Acceso a la Aplicación

Una vez iniciada:
- **🎨 Frontend**: http://localhost:3000 o http://localhost:3001
- **📊 Backend API**: http://localhost:8000
- **📚 Documentación**: http://localhost:8000/docs
- **📖 ReDoc**: http://localhost:8000/redoc

## 📁 Estructura del Proyecto

```
lentes/
├── backend/
│   ├── app/
│   │   ├── api/v1/endpoints/     # Endpoints de la API
│   │   ├── core/                 # Configuración central
│   │   ├── services/             # Lógica de negocio
│   │   ├── schemas/              # Esquemas Pydantic
│   │   ├── models/               # Modelos de datos
│   │   └── utils/                # Utilidades
│   ├── tests/                    # Tests unitarios e integración
│   ├── static/
│   │   ├── lenses/               # Imágenes de lentes
│   │   └── temp/                 # Archivos temporales
│   └── requirements.txt
├── frontend/
│   ├── src/
│   │   ├── components/           # Componentes React
│   │   ├── services/             # Servicios API
│   │   ├── utils/                # Utilidades frontend
│   │   └── assets/               # Recursos estáticos
│   └── package.json
├── docs/                         # Documentación
└── scripts/                      # Scripts de automatización
```

## 🔧 Funcionalidades

### ✅ Implementadas
- [x] Estructura base del proyecto
- [x] Configuración de FastAPI con CORS
- [x] Configuración de React con Vite
- [x] Sistema de logging estructurado
- [x] Endpoints de salud básicos

### 🚧 En Desarrollo
- [ ] Detección de landmarks faciales con MediaPipe
- [ ] Superposición de lentes con transformaciones geométricas
- [ ] API REST para procesamiento de imágenes
- [ ] Interfaz web para captura de cámara
- [ ] Selector de lentes interactivo
- [ ] Vista previa en tiempo real

### 📋 Próximas Funcionalidades
- [ ] Comparación lado a lado de diferentes lentes
- [ ] Descarga de imágenes procesadas
- [ ] Ajustes avanzados (escala, posición, rotación)
- [ ] Catálogo de lentes expandido
- [ ] Tests automatizados completos
- [ ] Optimizaciones de rendimiento

## 🧪 Testing

### Backend
```bash
cd backend
pytest tests/ -v
```

### Frontend
```bash
cd frontend
npm run test
```

## 📊 Métricas de Calidad

- **Cobertura de tests**: Objetivo 80%+
- **Tiempo de respuesta API**: <500ms para procesamiento de imagen
- **Precisión de detección**: >90% en condiciones normales de iluminación
- **Compatibilidad**: Chrome 90+, Firefox 88+, Safari 14+

## 🔒 Consideraciones de Seguridad

- Validación estricta de tipos de archivo (JPEG, PNG, WebP)
- Límite de tamaño de archivo: 10MB
- Sanitización de metadatos de imagen
- No persistencia de imágenes de usuario sin consentimiento
- Logging sin información sensible

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 🆘 Soporte

Para reportar bugs o solicitar nuevas funcionalidades, por favor abre un issue en el repositorio.

---

**Desarrollado con ❤️ usando FastAPI, React y MediaPipe**
