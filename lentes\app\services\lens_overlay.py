"""
Servicio de superposición de lentes virtuales.

Ruta: app/services/lens_overlay.py
Responsabilidad: Aplicar lentes virtuales sobre rostros detectados
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import structlog
from PIL import Image, ImageDraw
import math
import os
from pathlib import Path

from app.schemas.lens_overlay import LensType

logger = structlog.get_logger()


class LensOverlayService:
    """Servicio para aplicar superposiciones de lentes virtuales."""
    
    def __init__(self):
        """Inicializa el servicio de superposición de lentes."""
        # Ruta base para las imágenes de lentes
        self.lens_images_path = Path("static/lenses")

        # Mapeo de colores a valores BGR (para fallback)
        self.color_map = {
            'blue': (255, 0, 0),      # BGR format
            'red': (0, 0, 255),
            'green': (0, 255, 0),
            'black': (0, 0, 0),
            'brown': (42, 42, 165),
            'gray': (128, 128, 128),
            'gold': (0, 215, 255),
            'silver': (192, 192, 192)
        }

        # Cache para imágenes de lentes cargadas
        self._lens_cache = {}

        logger.info("LensOverlayService inicializado")

    def _load_lens_image(self, lens_type: LensType, color: str) -> Optional[np.ndarray]:
        """
        Carga una imagen de lente desde el sistema de archivos.

        Args:
            lens_type: Tipo de lente
            color: Color del lente

        Returns:
            Imagen del lente en formato OpenCV (BGRA) o None si no se encuentra
        """
        try:
            # Crear clave para el cache
            cache_key = f"{lens_type.value}_{color}"

            # Verificar cache
            if cache_key in self._lens_cache:
                return self._lens_cache[cache_key]

            # Construir ruta del archivo
            filename = f"{lens_type.value}_{color}.png"
            lens_path = self.lens_images_path / filename

            if not lens_path.exists():
                logger.warning(f"Imagen de lente no encontrada: {lens_path}")
                return None

            # Cargar imagen con canal alpha
            lens_image = cv2.imread(str(lens_path), cv2.IMREAD_UNCHANGED)

            if lens_image is None:
                logger.error(f"No se pudo cargar la imagen: {lens_path}")
                return None

            # Guardar en cache
            self._lens_cache[cache_key] = lens_image
            logger.debug(f"Imagen de lente cargada: {filename}")

            return lens_image

        except Exception as e:
            logger.error(f"Error cargando imagen de lente: {str(e)}")
            return None

    def apply_lens(self, image: np.ndarray, faces: List[Dict],
                   lens_type: LensType, lens_color: str = "blue") -> np.ndarray:
        """
        Aplica lentes virtuales a todos los rostros detectados.
        
        Args:
            image: Imagen original en formato OpenCV (BGR)
            faces: Lista de rostros detectados con landmarks
            lens_type: Tipo de lente a aplicar
            lens_color: Color del lente
            
        Returns:
            Imagen con lentes aplicados
        """
        try:
            result_image = image.copy()
            
            for face in faces:
                if face.get('landmarks'):
                    result_image = self._apply_lens_to_face(
                        result_image, face, lens_type, lens_color
                    )
                else:
                    # Si no hay landmarks, usar detección básica
                    result_image = self._apply_basic_lens(
                        result_image, face, lens_type, lens_color
                    )
            
            logger.info(f"Lentes {lens_type.value} aplicados a {len(faces)} rostros")
            return result_image
            
        except Exception as e:
            logger.error(f"Error aplicando lentes: {str(e)}")
            return image
    
    def _apply_lens_to_face(self, image: np.ndarray, face: Dict, 
                           lens_type: LensType, lens_color: str) -> np.ndarray:
        """
        Aplica lentes a un rostro específico usando landmarks.
        
        Args:
            image: Imagen a modificar
            face: Información del rostro con landmarks
            lens_type: Tipo de lente
            lens_color: Color del lente
            
        Returns:
            Imagen modificada
        """
        try:
            landmarks = face['landmarks']
            
            # Obtener puntos clave
            left_eye = landmarks.get('left_eye_center')
            right_eye = landmarks.get('right_eye_center')
            
            if not left_eye or not right_eye:
                return self._apply_basic_lens(image, face, lens_type, lens_color)
            
            # Calcular parámetros de los lentes
            eye_distance = math.sqrt((right_eye[0] - left_eye[0])**2 + 
                                   (right_eye[1] - left_eye[1])**2)
            
            # Calcular centro entre los ojos
            center_x = int((left_eye[0] + right_eye[0]) / 2)
            center_y = int((left_eye[1] + right_eye[1]) / 2)
            
            # Calcular ángulo de rotación
            angle = math.atan2(right_eye[1] - left_eye[1], 
                             right_eye[0] - left_eye[0])
            
            # Aplicar lentes usando imagen real
            return self._apply_lens_image(image, left_eye, right_eye,
                                        center_x, center_y, eye_distance,
                                        angle, lens_type, lens_color)
                
        except Exception as e:
            logger.error(f"Error aplicando lentes con landmarks: {str(e)}")
            return image
    
    def _apply_basic_lens(self, image: np.ndarray, face: Dict, 
                         lens_type: LensType, lens_color: str) -> np.ndarray:
        """
        Aplica lentes usando solo la detección básica del rostro.
        
        Args:
            image: Imagen a modificar
            face: Información básica del rostro
            lens_type: Tipo de lente
            lens_color: Color del lente
            
        Returns:
            Imagen modificada
        """
        try:
            x, y, w, h = face['x'], face['y'], face['width'], face['height']
            
            # Estimar posición de los ojos
            eye_y = y + int(h * 0.35)  # Los ojos están aproximadamente al 35% de la altura
            left_eye_x = x + int(w * 0.25)   # Ojo izquierdo al 25% del ancho
            right_eye_x = x + int(w * 0.75)  # Ojo derecho al 75% del ancho
            
            left_eye = (left_eye_x, eye_y)
            right_eye = (right_eye_x, eye_y)
            
            eye_distance = right_eye_x - left_eye_x
            center_x = x + w // 2
            center_y = eye_y
            
            # Aplicar lentes básicos usando imagen real
            return self._apply_lens_image(image, left_eye, right_eye,
                                        center_x, center_y, eye_distance,
                                        0, lens_type, lens_color)
                                    
        except Exception as e:
            logger.error(f"Error aplicando lentes básicos: {str(e)}")
            return image

    def _apply_lens_image(self, image: np.ndarray, left_eye: Tuple[int, int],
                         right_eye: Tuple[int, int], center_x: int, center_y: int,
                         eye_distance: float, angle: float, lens_type: LensType,
                         lens_color: str) -> np.ndarray:
        """
        Aplica una imagen real de lentes sobre el rostro.

        Args:
            image: Imagen base
            left_eye: Posición del ojo izquierdo
            right_eye: Posición del ojo derecho
            center_x: Centro X entre los ojos
            center_y: Centro Y entre los ojos
            eye_distance: Distancia entre los ojos
            angle: Ángulo de rotación
            lens_type: Tipo de lente
            lens_color: Color del lente

        Returns:
            Imagen con lentes aplicados
        """
        try:
            # Cargar imagen del lente
            lens_image = self._load_lens_image(lens_type, lens_color)

            if lens_image is None:
                # Fallback a dibujo simple si no hay imagen
                return self._draw_simple_glasses(image, left_eye, right_eye,
                                               eye_distance, lens_color)

            # Calcular escala basada en la distancia entre ojos
            # La imagen base es de 300x200, ajustar proporcionalmente
            scale_factor = eye_distance / 120.0  # 120 es la distancia base en la imagen

            # Redimensionar imagen del lente
            new_width = int(lens_image.shape[1] * scale_factor)
            new_height = int(lens_image.shape[0] * scale_factor)

            if new_width > 0 and new_height > 0:
                resized_lens = cv2.resize(lens_image, (new_width, new_height))

                # Calcular posición para centrar los lentes
                pos_x = center_x - new_width // 2
                pos_y = center_y - new_height // 2

                # Aplicar rotación si es necesaria
                if abs(angle) > 0.1:  # Solo rotar si el ángulo es significativo
                    resized_lens = self._rotate_image(resized_lens, math.degrees(angle))

                # Superponer la imagen del lente
                image = self._overlay_image(image, resized_lens, pos_x, pos_y)

            return image

        except Exception as e:
            logger.error(f"Error aplicando imagen de lente: {str(e)}")
            # Fallback a dibujo simple
            return self._draw_simple_glasses(image, left_eye, right_eye,
                                           eye_distance, lens_color)

    def _rotate_image(self, image: np.ndarray, angle: float) -> np.ndarray:
        """
        Rota una imagen por el ángulo especificado.

        Args:
            image: Imagen a rotar
            angle: Ángulo en grados

        Returns:
            Imagen rotada
        """
        try:
            height, width = image.shape[:2]
            center = (width // 2, height // 2)

            # Crear matriz de rotación
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)

            # Aplicar rotación
            rotated = cv2.warpAffine(image, rotation_matrix, (width, height),
                                   flags=cv2.INTER_LINEAR,
                                   borderMode=cv2.BORDER_TRANSPARENT)

            return rotated

        except Exception as e:
            logger.error(f"Error rotando imagen: {str(e)}")
            return image

    def _overlay_image(self, background: np.ndarray, overlay: np.ndarray,
                      x: int, y: int) -> np.ndarray:
        """
        Superpone una imagen con transparencia sobre otra.

        Args:
            background: Imagen de fondo
            overlay: Imagen a superponer (con canal alpha)
            x: Posición X
            y: Posición Y

        Returns:
            Imagen combinada
        """
        try:
            bg_h, bg_w = background.shape[:2]
            ov_h, ov_w = overlay.shape[:2]

            # Verificar límites
            if x >= bg_w or y >= bg_h or x + ov_w <= 0 or y + ov_h <= 0:
                return background

            # Calcular región de superposición
            x1 = max(0, x)
            y1 = max(0, y)
            x2 = min(bg_w, x + ov_w)
            y2 = min(bg_h, y + ov_h)

            # Calcular región en la imagen de superposición
            ov_x1 = max(0, -x)
            ov_y1 = max(0, -y)
            ov_x2 = ov_x1 + (x2 - x1)
            ov_y2 = ov_y1 + (y2 - y1)

            # Extraer regiones
            bg_region = background[y1:y2, x1:x2]
            ov_region = overlay[ov_y1:ov_y2, ov_x1:ov_x2]

            if ov_region.shape[2] == 4:  # Tiene canal alpha
                # Separar canales
                ov_rgb = ov_region[:, :, :3]
                alpha = ov_region[:, :, 3:4] / 255.0

                # Aplicar blending
                blended = bg_region * (1 - alpha) + ov_rgb * alpha
                background[y1:y2, x1:x2] = blended.astype(np.uint8)
            else:
                # Sin transparencia, copiar directamente
                background[y1:y2, x1:x2] = ov_region

            return background

        except Exception as e:
            logger.error(f"Error superponiendo imagen: {str(e)}")
            return background

    def _draw_simple_glasses(self, image: np.ndarray, left_eye: Tuple[int, int],
                           right_eye: Tuple[int, int], eye_distance: float,
                           color: str) -> np.ndarray:
        """
        Dibuja lentes simples como fallback cuando no hay imagen disponible.

        Args:
            image: Imagen base
            left_eye: Posición del ojo izquierdo
            right_eye: Posición del ojo derecho
            eye_distance: Distancia entre los ojos
            color: Color del lente

        Returns:
            Imagen con lentes simples dibujados
        """
        try:
            color_bgr = self.color_map.get(color, self.color_map['blue'])

            # Calcular tamaño de los lentes
            lens_radius = int(eye_distance * 0.25)
            frame_thickness = max(2, int(lens_radius * 0.1))

            # Dibujar lentes izquierdo y derecho
            cv2.circle(image, left_eye, lens_radius, color_bgr, frame_thickness)
            cv2.circle(image, right_eye, lens_radius, color_bgr, frame_thickness)

            # Dibujar puente nasal
            bridge_start = (left_eye[0] + int(lens_radius * 0.7), left_eye[1])
            bridge_end = (right_eye[0] - int(lens_radius * 0.7), right_eye[1])
            cv2.line(image, bridge_start, bridge_end, color_bgr, frame_thickness)

            # Dibujar patillas
            temple_length = int(eye_distance * 0.4)
            left_temple_end = (left_eye[0] - temple_length, left_eye[1])
            right_temple_end = (right_eye[0] + temple_length, right_eye[1])

            cv2.line(image, left_eye, left_temple_end, color_bgr, frame_thickness)
            cv2.line(image, right_eye, right_temple_end, color_bgr, frame_thickness)

            return image

        except Exception as e:
            logger.error(f"Error dibujando lentes simples: {str(e)}")
            return image
