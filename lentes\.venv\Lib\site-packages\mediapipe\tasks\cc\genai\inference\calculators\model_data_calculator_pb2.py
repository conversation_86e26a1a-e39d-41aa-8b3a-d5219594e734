# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/genai/inference/calculators/model_data_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nJmediapipe/tasks/cc/genai/inference/calculators/model_data_calculator.proto\x12\x10odml.infra.proto\"O\n\x1aModelDataCalculatorOptions\x12\x13\n\x0bweight_path\x18\x01 \x01(\t\x12\x16\n\x0espm_model_file\x18\x03 \x01(\tJ\x04\x08\x02\x10\x03\x42>\n\x1b\x63om.google.odml.infra.protoB\x1fModelDataCalculatorOptionsProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.genai.inference.calculators.model_data_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\033com.google.odml.infra.protoB\037ModelDataCalculatorOptionsProto'
  _globals['_MODELDATACALCULATOROPTIONS']._serialized_start=96
  _globals['_MODELDATACALCULATOROPTIONS']._serialized_end=175
# @@protoc_insertion_point(module_scope)
