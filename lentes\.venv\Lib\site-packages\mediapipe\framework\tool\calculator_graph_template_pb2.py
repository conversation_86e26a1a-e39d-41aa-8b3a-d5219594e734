# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/tool/calculator_graph_template.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2
from mediapipe.framework import calculator_options_pb2 as mediapipe_dot_framework_dot_calculator__options__pb2
from mediapipe.framework.deps import proto_descriptor_pb2 as mediapipe_dot_framework_dot_deps_dot_proto__descriptor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8mediapipe/framework/tool/calculator_graph_template.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\x1a,mediapipe/framework/calculator_options.proto\x1a/mediapipe/framework/deps/proto_descriptor.proto\"\xf0\x01\n\x12TemplateExpression\x12\r\n\x05param\x18\x01 \x01(\t\x12\n\n\x02op\x18\x02 \x01(\t\x12*\n\x03\x61rg\x18\x03 \x03(\x0b\x32\x1d.mediapipe.TemplateExpression\x12\x0c\n\x04path\x18\x04 \x01(\t\x12\x38\n\nfield_type\x18\x05 \x01(\x0e\x32$.mediapipe.FieldDescriptorProto.Type\x12\x36\n\x08key_type\x18\x06 \x03(\x0e\x32$.mediapipe.FieldDescriptorProto.Type\x12\x13\n\x0b\x66ield_value\x18\x07 \x01(\t\"x\n\x17\x43\x61lculatorGraphTemplate\x12\x30\n\x06\x63onfig\x18\x01 \x01(\x0b\x32 .mediapipe.CalculatorGraphConfig\x12+\n\x04rule\x18\x02 \x03(\x0b\x32\x1d.mediapipe.TemplateExpression\"\x96\x01\n\x10TemplateArgument\x12\r\n\x03str\x18\x01 \x01(\tH\x00\x12\r\n\x03num\x18\x02 \x01(\x01H\x00\x12\'\n\x04\x64ict\x18\x03 \x01(\x0b\x32\x17.mediapipe.TemplateDictH\x00\x12,\n\x07\x65lement\x18\x04 \x03(\x0b\x32\x1b.mediapipe.TemplateArgumentB\r\n\x0bparam_value\"\x84\x01\n\x0cTemplateDict\x12.\n\x03\x61rg\x18\x01 \x03(\x0b\x32!.mediapipe.TemplateDict.Parameter\x1a\x44\n\tParameter\x12\x0b\n\x03key\x18\x01 \x01(\t\x12*\n\x05value\x18\x02 \x01(\x0b\x32\x1b.mediapipe.TemplateArgument\"\x92\x01\n\x17TemplateSubgraphOptions\x12%\n\x04\x64ict\x18\x01 \x01(\x0b\x32\x17.mediapipe.TemplateDict2P\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xf5\xfc\xbeR \x01(\x0b\x32\".mediapipe.TemplateSubgraphOptionsB0\n\x1a\x63om.google.mediapipe.protoB\x12GraphTemplateProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.tool.calculator_graph_template_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_TEMPLATESUBGRAPHOPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\032com.google.mediapipe.protoB\022GraphTemplateProto'
  _globals['_TEMPLATEEXPRESSION']._serialized_start=205
  _globals['_TEMPLATEEXPRESSION']._serialized_end=445
  _globals['_CALCULATORGRAPHTEMPLATE']._serialized_start=447
  _globals['_CALCULATORGRAPHTEMPLATE']._serialized_end=567
  _globals['_TEMPLATEARGUMENT']._serialized_start=570
  _globals['_TEMPLATEARGUMENT']._serialized_end=720
  _globals['_TEMPLATEDICT']._serialized_start=723
  _globals['_TEMPLATEDICT']._serialized_end=855
  _globals['_TEMPLATEDICT_PARAMETER']._serialized_start=787
  _globals['_TEMPLATEDICT_PARAMETER']._serialized_end=855
  _globals['_TEMPLATESUBGRAPHOPTIONS']._serialized_start=858
  _globals['_TEMPLATESUBGRAPHOPTIONS']._serialized_end=1004
# @@protoc_insertion_point(module_scope)
