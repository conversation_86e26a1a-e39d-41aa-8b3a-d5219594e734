# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/vision/face_geometry/proto/face_geometry.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework.formats import matrix_data_pb2 as mediapipe_dot_framework_dot_formats_dot_matrix__data__pb2
from mediapipe.tasks.cc.vision.face_geometry.proto import mesh_3d_pb2 as mediapipe_dot_tasks_dot_cc_dot_vision_dot_face__geometry_dot_proto_dot_mesh__3d__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nAmediapipe/tasks/cc/vision/face_geometry/proto/face_geometry.proto\x12*mediapipe.tasks.vision.face_geometry.proto\x1a-mediapipe/framework/formats/matrix_data.proto\x1a;mediapipe/tasks/cc/vision/face_geometry/proto/mesh_3d.proto\"\x86\x01\n\x0c\x46\x61\x63\x65Geometry\x12@\n\x04mesh\x18\x01 \x01(\x0b\x32\x32.mediapipe.tasks.vision.face_geometry.proto.Mesh3d\x12\x34\n\x15pose_transform_matrix\x18\x02 \x01(\x0b\x32\x15.mediapipe.MatrixDataBI\n4com.google.mediapipe.tasks.vision.facegeometry.protoB\x11\x46\x61\x63\x65GeometryProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.vision.face_geometry.proto.face_geometry_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n4com.google.mediapipe.tasks.vision.facegeometry.protoB\021FaceGeometryProto'
  _globals['_FACEGEOMETRY']._serialized_start=222
  _globals['_FACEGEOMETRY']._serialized_end=356
# @@protoc_insertion_point(module_scope)
