/* Estilos del carrito de compras */

.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 2000;
  display: flex;
  justify-content: flex-end;
}

.cart-sidebar {
  width: 500px;
  max-width: 90vw;
  height: 100vh;
  background: white;
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid #e1e8ed;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
}

.cart-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.cart-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}

/* Carrito vacío */
.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #7f8c8d;
}

.empty-cart-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-cart h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.empty-cart p {
  font-size: 1rem;
  line-height: 1.5;
}

/* Items del carrito */
.cart-items {
  flex: 1;
  margin-bottom: 1.5rem;
}

.cart-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e1e8ed;
  border-radius: 10px;
  margin-bottom: 1rem;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.cart-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.item-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.item-details h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  line-height: 1.3;
}

.item-brand {
  font-size: 0.85rem;
  color: #7f8c8d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.item-color {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #2c3e50;
}

.color-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid #e1e8ed;
}

.item-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3498db;
  margin-top: auto;
}

.item-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 20px;
  padding: 0.25rem;
}

.quantity-controls button {
  width: 30px;
  height: 30px;
  border: none;
  background: #3498db;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.quantity-controls button:hover:not(:disabled) {
  background: #2980b9;
  transform: scale(1.1);
}

.quantity-controls button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
}

.quantity-controls span {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
}

.remove-btn {
  background: #e74c3c;
  border: none;
  color: white;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: #c0392b;
  transform: scale(1.1);
}

/* Resumen de precios */
.cart-summary {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e1e8ed;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.summary-line:last-child {
  margin-bottom: 0;
}

.summary-line.total {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
  padding-top: 0.75rem;
  border-top: 2px solid #3498db;
}

.shipping-notice {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  padding: 0.75rem;
  border-radius: 8px;
  margin-top: 1rem;
  text-align: center;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Formulario de envío */
.shipping-form {
  margin-bottom: 1.5rem;
}

.shipping-form h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-grid input {
  padding: 0.75rem;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: border-color 0.3s ease;
}

.form-grid input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.form-grid input.full-width {
  grid-column: 1 / -1;
}

.shipping-methods h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.shipping-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.shipping-option:hover {
  border-color: #3498db;
  background: #f8f9fa;
}

.shipping-option input[type="radio"] {
  margin: 0;
}

.shipping-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.shipping-name {
  font-weight: 600;
  color: #2c3e50;
}

.shipping-time {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.shipping-cost {
  font-weight: 600;
  color: #3498db;
}

/* Botón de checkout */
.checkout-btn {
  width: 100%;
  padding: 1.25rem;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.checkout-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.checkout-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.checkout-btn.loading {
  pointer-events: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .cart-sidebar {
    width: 100vw;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .shipping-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .cart-item {
    flex-direction: column;
    gap: 1rem;
  }
  
  .item-controls {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
  }
}
