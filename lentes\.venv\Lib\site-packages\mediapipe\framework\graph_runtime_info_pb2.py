# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/graph_runtime_info.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,mediapipe/framework/graph_runtime_info.proto\x12\tmediapipe\"\x81\x01\n\x11StreamRuntimeInfo\x12\x13\n\x0bstream_name\x18\x01 \x01(\t\x12\x12\n\nqueue_size\x18\x02 \x01(\x05\x12\x1f\n\x17number_of_packets_added\x18\x03 \x01(\x05\x12\"\n\x1aminimum_timestamp_or_bound\x18\x04 \x01(\x03\"\xcc\x01\n\x15\x43\x61lculatorRuntimeInfo\x12\x17\n\x0f\x63\x61lculator_name\x18\x01 \x01(\t\x12\"\n\x1alast_process_start_unix_us\x18\x02 \x01(\x03\x12#\n\x1blast_process_finish_unix_us\x18\x03 \x01(\x03\x12\x17\n\x0ftimestamp_bound\x18\x04 \x01(\x03\x12\x38\n\x12input_stream_infos\x18\x05 \x03(\x0b\x32\x1c.mediapipe.StreamRuntimeInfo\"l\n\x10GraphRuntimeInfo\x12\x1c\n\x14\x63\x61pture_time_unix_us\x18\x01 \x01(\x03\x12:\n\x10\x63\x61lculator_infos\x18\x02 \x03(\x0b\x32 .mediapipe.CalculatorRuntimeInfoB3\n\x1a\x63om.google.mediapipe.protoB\x15GraphRuntimeInfoProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.graph_runtime_info_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\032com.google.mediapipe.protoB\025GraphRuntimeInfoProto'
  _globals['_STREAMRUNTIMEINFO']._serialized_start=60
  _globals['_STREAMRUNTIMEINFO']._serialized_end=189
  _globals['_CALCULATORRUNTIMEINFO']._serialized_start=192
  _globals['_CALCULATORRUNTIMEINFO']._serialized_end=396
  _globals['_GRAPHRUNTIMEINFO']._serialized_start=398
  _globals['_GRAPHRUNTIMEINFO']._serialized_end=506
# @@protoc_insertion_point(module_scope)
