/* Estilos del catálogo de productos */

.product-catalog {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background: transparent;
}

.catalog-header {
  text-align: center;
  margin-bottom: 3rem;
}

.catalog-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.catalog-header p {
  font-size: 1.2rem;
  color: #7f8c8d;
  max-width: 600px;
  margin: 0 auto;
}

.catalog-container {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 3rem;
  align-items: start;
}

/* Filtros laterales */
.filters-sidebar {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.filter-section {
  margin-bottom: 2rem;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-section h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #3498db;
}

.category-filters {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 0.95rem;
}

.category-btn:hover {
  border-color: #3498db;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
}

.category-btn.active {
  border-color: #3498db;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.category-icon {
  font-size: 1.2rem;
}

.brand-select, .sort-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  font-size: 1rem;
  background: white;
  transition: border-color 0.3s ease;
}

.brand-select:focus, .sort-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.price-range {
  margin-top: 0.5rem;
}

.price-slider {
  width: 100%;
  margin-bottom: 0.5rem;
}

.price-display {
  text-align: center;
  font-weight: 600;
  color: #3498db;
  font-size: 1.1rem;
}

/* Productos principales */
.products-main {
  min-height: 600px;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.results-info {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sort-controls label {
  font-weight: 500;
  color: #2c3e50;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

/* Tarjetas de productos */
.product-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
  position: relative;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
}

.new-badge {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  color: white;
}

.sale-badge {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.stock-badge {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: white;
}

.product-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.try-virtual-btn {
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.try-virtual-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

.product-info {
  padding: 1.5rem;
}

.product-brand {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-weight: 500;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.product-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.stars {
  color: #f39c12;
  font-size: 1rem;
}

.rating-text {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.current-price {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
}

.original-price {
  font-size: 1rem;
  color: #7f8c8d;
  text-decoration: line-through;
}

.product-colors {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.color-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #e1e8ed;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-dot:hover {
  transform: scale(1.2);
  border-color: #3498db;
}

.add-to-cart-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-to-cart-btn:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.add-to-cart-btn.disabled {
  background: #95a5a6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive design */
@media (max-width: 1024px) {
  .catalog-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .filters-sidebar {
    position: static;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
  
  .filter-section {
    margin-bottom: 0;
  }
}

@media (max-width: 768px) {
  .product-catalog {
    padding: 1rem;
  }
  
  .catalog-header h2 {
    font-size: 2rem;
  }
  
  .products-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
  }
  
  .filters-sidebar {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .product-card {
    margin: 0 auto;
    max-width: 350px;
  }
}
