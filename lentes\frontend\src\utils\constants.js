/**
 * Constantes de la aplicación
 */

// Tipos de lentes disponibles
export const LENS_TYPES = [
  {
    value: 'glasses',
    label: 'Lentes Normales',
    description: 'Lentes clásicos para uso diario',
    icon: '👓'
  },
  {
    value: 'sunglasses',
    label: 'Lentes de Sol',
    description: 'Lentes oscuros para protección solar',
    icon: '🕶️'
  },
  {
    value: 'reading_glasses',
    label: 'Lentes de Lectura',
    description: 'Lentes para lectura y trabajo de cerca',
    icon: '🤓'
  },
  {
    value: 'safety_glasses',
    label: 'Lentes de Seguridad',
    description: 'Lentes de protección industrial',
    icon: '🥽'
  },
  {
    value: 'fashion_glasses',
    label: 'Lentes de Moda',
    description: 'Lentes con diseño moderno y elegante',
    icon: '👓'
  }
]

// Colores disponibles para lentes
export const LENS_COLORS = [
  {
    value: 'blue',
    label: 'Azul',
    hex: '#0066cc',
    description: 'Azul clásico'
  },
  {
    value: 'red',
    label: 'Rojo',
    hex: '#cc0000',
    description: 'Rojo vibrante'
  },
  {
    value: 'green',
    label: 'Verde',
    hex: '#00cc00',
    description: 'Verde natural'
  },
  {
    value: 'black',
    label: 'Negro',
    hex: '#000000',
    description: 'Negro elegante'
  },
  {
    value: 'brown',
    label: 'Marrón',
    hex: '#8B4513',
    description: 'Marrón cálido'
  },
  {
    value: 'gray',
    label: 'Gris',
    hex: '#808080',
    description: 'Gris neutro'
  },
  {
    value: 'gold',
    label: 'Dorado',
    hex: '#FFD700',
    description: 'Dorado brillante'
  },
  {
    value: 'silver',
    label: 'Plateado',
    hex: '#C0C0C0',
    description: 'Plateado metálico'
  }
]

// Configuración de archivos
export const FILE_CONFIG = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.webp']
}

// Mensajes de error
export const ERROR_MESSAGES = {
  NO_FILE: 'No se ha seleccionado ningún archivo',
  INVALID_TYPE: 'Tipo de archivo no válido. Solo se permiten: JPEG, PNG, WebP',
  FILE_TOO_LARGE: 'El archivo es demasiado grande. Tamaño máximo: 10MB',
  NO_FACES: 'No se detectaron rostros en la imagen. Intenta con otra imagen.',
  NETWORK_ERROR: 'Error de conexión. Verifica tu conexión a internet.',
  SERVER_ERROR: 'Error del servidor. Intenta nuevamente en unos momentos.',
  PROCESSING_ERROR: 'Error procesando la imagen. Intenta nuevamente.',
  UNKNOWN_ERROR: 'Ha ocurrido un error inesperado.'
}

// Mensajes de éxito
export const SUCCESS_MESSAGES = {
  LENS_APPLIED: 'Lentes aplicados exitosamente',
  IMAGE_DOWNLOADED: 'Imagen descargada correctamente',
  PROCESSING_COMPLETE: 'Procesamiento completado'
}

// Estados de la aplicación
export const APP_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
}

// Configuración de la API
export const API_CONFIG = {
  BASE_URL: '/api/v1',
  TIMEOUT: 30000, // 30 segundos
  ENDPOINTS: {
    APPLY_LENS: '/lens/apply',
    APPLY_LENS_STREAM: '/lens/apply-stream',
    LENS_TYPES: '/lens/lens-types',
    HEALTH: '/health',
    ROOT: '/'
  }
}

// Configuración de UI
export const UI_CONFIG = {
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
  MAX_PREVIEW_WIDTH: 800,
  MAX_PREVIEW_HEIGHT: 600,
  THUMBNAIL_SIZE: 150
}

// Temas de color
export const THEMES = {
  PRIMARY: {
    main: '#667eea',
    light: '#8fa4f3',
    dark: '#4c63d2'
  },
  SECONDARY: {
    main: '#764ba2',
    light: '#9575cd',
    dark: '#512da8'
  },
  SUCCESS: {
    main: '#00b894',
    light: '#26d0ce',
    dark: '#00a085'
  },
  ERROR: {
    main: '#ff6b6b',
    light: '#ff8a80',
    dark: '#e53935'
  },
  WARNING: {
    main: '#fdcb6e',
    light: '#fff176',
    dark: '#f57c00'
  }
}

// Breakpoints para responsive design
export const BREAKPOINTS = {
  XS: '480px',
  SM: '768px',
  MD: '1024px',
  LG: '1200px',
  XL: '1440px'
}

export default {
  LENS_TYPES,
  LENS_COLORS,
  FILE_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  APP_STATES,
  API_CONFIG,
  UI_CONFIG,
  THEMES,
  BREAKPOINTS
}
