/**
 * Servicio de API para comunicación con el backend
 */

import axios from 'axios'

// Configuración base de axios
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 30000, // 30 segundos
  headers: {
    'Content-Type': 'application/json',
  },
})

// Interceptor para requests
api.interceptors.request.use(
  (config) => {
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('❌ API Request Error:', error)
    return Promise.reject(error)
  }
)

// Interceptor para responses
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.status, error.response?.data)
    return Promise.reject(error)
  }
)

/**
 * Servicio para endpoints de lentes
 */
export const lensService = {
  /**
   * Aplica lentes a una imagen y retorna la respuesta JSON
   */
  async applyLens(imageFile, lensType = 'glasses', lensColor = 'blue') {
    const formData = new FormData()
    formData.append('image', imageFile)
    formData.append('lens_type', lensType)
    formData.append('lens_color', lensColor)

    const response = await api.post('/lens/apply', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })

    return response.data
  },

  /**
   * Aplica lentes a una imagen y retorna el stream de la imagen
   */
  async applyLensStream(imageFile, lensType = 'glasses', lensColor = 'blue') {
    const formData = new FormData()
    formData.append('image', imageFile)
    formData.append('lens_type', lensType)
    formData.append('lens_color', lensColor)

    const response = await api.post('/lens/apply-stream', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      responseType: 'blob',
    })

    return response
  },

  /**
   * Obtiene los tipos de lentes disponibles
   */
  async getLensTypes() {
    const response = await api.get('/lens/lens-types')
    return response.data
  },
}

/**
 * Servicio para endpoints generales
 */
export const generalService = {
  /**
   * Verifica el estado de salud de la API
   */
  async healthCheck() {
    const response = await api.get('/health')
    return response.data
  },

  /**
   * Obtiene información básica de la API
   */
  async getApiInfo() {
    const response = await api.get('/')
    return response.data
  },
}

export default api
