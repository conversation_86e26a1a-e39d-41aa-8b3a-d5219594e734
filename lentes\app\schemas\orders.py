"""
Esquemas Pydantic para el sistema de pedidos y envíos.

Ruta: app/schemas/orders.py
Responsabilidad: Definición de modelos de datos para pedidos y envíos
"""

from pydantic import BaseModel, Field
from enum import Enum
from typing import Optional, List
from datetime import datetime


class OrderStatus(str, Enum):
    """Estados de pedido."""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    PROCESSING = "processing"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"


class ShippingMethod(str, Enum):
    """Métodos de envío."""
    STANDARD = "standard"
    EXPRESS = "express"
    OVERNIGHT = "overnight"


class PaymentStatus(str, Enum):
    """Estados de pago."""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"


class ShippingAddress(BaseModel):
    """Dirección de envío."""
    name: str = Field(..., description="Nombre completo")
    email: str = Field(..., description="Email de contacto")
    phone: str = Field(..., description="Teléfono de contacto")
    address: str = Field(..., description="Dirección completa")
    city: str = Field(..., description="Ciudad")
    state: Optional[str] = Field(None, description="Estado/Provincia")
    zip_code: str = Field(..., description="Código postal")
    country: str = Field(default="US", description="País")


class OrderItem(BaseModel):
    """Item de pedido."""
    product_id: int = Field(..., description="ID del producto")
    product_name: str = Field(..., description="Nombre del producto")
    brand: str = Field(..., description="Marca del producto")
    selected_color: str = Field(..., description="Color seleccionado")
    quantity: int = Field(..., description="Cantidad", ge=1)
    unit_price: float = Field(..., description="Precio unitario", ge=0)
    total_price: float = Field(..., description="Precio total del item", ge=0)


class OrderTotals(BaseModel):
    """Totales del pedido."""
    subtotal: float = Field(..., description="Subtotal", ge=0)
    shipping_cost: float = Field(..., description="Costo de envío", ge=0)
    tax: float = Field(..., description="Impuestos", ge=0)
    discount: float = Field(default=0, description="Descuento", ge=0)
    total: float = Field(..., description="Total final", ge=0)


class CreateOrderRequest(BaseModel):
    """Solicitud para crear un pedido."""
    items: List[OrderItem] = Field(..., description="Items del pedido")
    shipping_address: ShippingAddress = Field(..., description="Dirección de envío")
    shipping_method: ShippingMethod = Field(..., description="Método de envío")
    totals: OrderTotals = Field(..., description="Totales del pedido")
    notes: Optional[str] = Field(None, description="Notas adicionales")


class TrackingEvent(BaseModel):
    """Evento de seguimiento."""
    timestamp: datetime = Field(..., description="Fecha y hora del evento")
    status: str = Field(..., description="Estado del evento")
    location: Optional[str] = Field(None, description="Ubicación del evento")
    description: str = Field(..., description="Descripción del evento")
    carrier: Optional[str] = Field(None, description="Transportista")


class ShippingInfo(BaseModel):
    """Información de envío."""
    tracking_number: Optional[str] = Field(None, description="Número de seguimiento")
    carrier: Optional[str] = Field(None, description="Transportista")
    estimated_delivery: Optional[datetime] = Field(None, description="Fecha estimada de entrega")
    actual_delivery: Optional[datetime] = Field(None, description="Fecha real de entrega")
    tracking_events: List[TrackingEvent] = Field(default=[], description="Eventos de seguimiento")


class Order(BaseModel):
    """Pedido completo."""
    id: int = Field(..., description="ID del pedido")
    order_number: str = Field(..., description="Número de pedido")
    status: OrderStatus = Field(..., description="Estado del pedido")
    payment_status: PaymentStatus = Field(..., description="Estado del pago")
    items: List[OrderItem] = Field(..., description="Items del pedido")
    shipping_address: ShippingAddress = Field(..., description="Dirección de envío")
    shipping_method: ShippingMethod = Field(..., description="Método de envío")
    shipping_info: Optional[ShippingInfo] = Field(None, description="Información de envío")
    totals: OrderTotals = Field(..., description="Totales del pedido")
    notes: Optional[str] = Field(None, description="Notas adicionales")
    created_at: datetime = Field(..., description="Fecha de creación")
    updated_at: datetime = Field(..., description="Fecha de actualización")


class OrderResponse(BaseModel):
    """Respuesta de pedido."""
    success: bool = Field(..., description="Indica si la operación fue exitosa")
    message: str = Field(..., description="Mensaje descriptivo")
    order: Optional[Order] = Field(None, description="Datos del pedido")
    estimated_delivery: Optional[str] = Field(None, description="Fecha estimada de entrega")


class TrackingResponse(BaseModel):
    """Respuesta de seguimiento."""
    success: bool = Field(..., description="Indica si la consulta fue exitosa")
    order_number: str = Field(..., description="Número de pedido")
    current_status: OrderStatus = Field(..., description="Estado actual")
    tracking_number: Optional[str] = Field(None, description="Número de seguimiento")
    estimated_delivery: Optional[datetime] = Field(None, description="Fecha estimada de entrega")
    tracking_events: List[TrackingEvent] = Field(..., description="Eventos de seguimiento")


class OrderListResponse(BaseModel):
    """Respuesta de lista de pedidos."""
    success: bool = Field(..., description="Indica si la consulta fue exitosa")
    orders: List[Order] = Field(..., description="Lista de pedidos")
    total_count: int = Field(..., description="Total de pedidos")
    page: int = Field(..., description="Página actual")
    per_page: int = Field(..., description="Items por página")


class UpdateOrderStatusRequest(BaseModel):
    """Solicitud para actualizar estado de pedido."""
    status: OrderStatus = Field(..., description="Nuevo estado")
    tracking_number: Optional[str] = Field(None, description="Número de seguimiento")
    carrier: Optional[str] = Field(None, description="Transportista")
    notes: Optional[str] = Field(None, description="Notas adicionales")
