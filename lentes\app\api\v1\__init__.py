"""
API v1 router configuration.

Ruta: app/api/v1/__init__.py
Responsabilidad: Configuración del router principal de la API v1
"""

from fastapi import APIRouter
from .endpoints import lens_overlay, orders

router = APIRouter()

# Incluir endpoints
router.include_router(lens_overlay.router, prefix="/lens", tags=["lens-overlay"])
router.include_router(orders.router, prefix="/orders", tags=["orders"])
